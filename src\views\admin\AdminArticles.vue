<template>
  <div class="admin-article">
    <h3>文章管理</h3>

    <!-- 工具栏 -->
    <div class="toolbar" style="margin-bottom: 20px">
      <div style="display: flex; gap: 10px; margin-bottom: 10px">
        <el-button type="success" @click="openDialog()">新增</el-button>
        <el-button
          type="danger"
          :disabled="!multipleSelection.length"
          @click="handleBatchDelete"
          >批量删除</el-button
        >
      </div>
      <div style="display: flex; flex-wrap: wrap; gap: 10px">
        <el-input
          v-model="keyword"
          placeholder="搜索标题,作者或相关内容"
          clearable
          style="width: 280px"
          @keyup.enter="loadArticles"
        />
        <el-select v-model="status" placeholder="状态" style="width: 150px">
          <el-option label="全部" value="" />
          <el-option label="草稿" value="DRAFT" />
          <el-option label="发布" value="PUBLISHED" />
        </el-select>
        <el-button type="primary" @click="loadArticles">搜索</el-button>
        <el-button @click="resetFilters">重置</el-button>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      :data="articles"
      v-loading="loading"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="ID" width="60">
        <template #default="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" width="300px" />
      <el-table-column prop="author" label="作者" width="200px" />
      <el-table-column label="内容" width="300px">
        <template #default="scope">
          {{
            scope.row.content.replace(/<[^>]+>/g, "").slice(0, 40) +
            (scope.row.content.length > 40 ? "..." : "")
          }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-tag type="info" v-if="scope.row.status === 'DRAFT'">草稿</el-tag>
          <el-tag type="success" v-else>发布</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" width="180">
        <template #default="scope">
          {{
            scope.row.updatedTime
              ? new Date(scope.row.updatedTime).toLocaleString()
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="260" fixed="right" align="center">
        <template #default="scope">
          <el-button size="small" type="success" @click="openDialog(scope.row)"
            >编辑</el-button
          >
          <el-button
            size="small"
            type="warning"
            @click="toggleStatus(scope.row)"
          >
            {{ scope.row.status === "PUBLISHED" ? "设为草稿" : "点击发布" }}
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="deleteSingle(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
      />
    </div>

    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px">
      <el-form :model="form" label-width="60px" label-position="left">
        <el-form-item label="标题">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="作者">
          <el-input v-model="form.author" placeholder="请输入作者" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option label="草稿" value="DRAFT" />
            <el-option label="发布" value="PUBLISHED" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容">
          <Toolbar :editor="editorRef" style="border-bottom: 1px solid #ccc" />
          <Editor
            v-model="form.content"
            :defaultConfig="editorConfig"
            :mode="'default'"
            @onCreated="handleEditorCreated"
            style="height: 300px; overflow-y: auto; border: 1px solid #ccc"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import "@wangeditor/editor/dist/css/style.css";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { ref, onMounted, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";

import {
  fetchArticlePage,
  addArticle,
  updateArticle,
  deleteArticle,
  deleteArticles,
  updateArticleStatus,
} from "@/api/article";

// 原有数据定义
const keyword = ref("");
const status = ref("");
const articles = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = 10;
const total = ref(0);
const multipleSelection = ref([]);

const dialogVisible = ref(false);
const dialogTitle = ref("新增文章");
const editId = ref(null);
const form = ref({ title: "", author: "", status: "DRAFT", content: "" });

// wangEditor 富文本部分
const editorRef = ref(null);
const editorConfig = {
  placeholder: "请输入文章内容...",
  MENU_CONF: {
    uploadImage: {
      server: "/api/admin/upload",
      fieldName: "file",
      // 👇添加这个函数，手动提取 URL 并插入图片
      customInsert(res, insertFn) {
        if (res.success && res.data) {
          insertFn(res.data); // res.data 是图片链接
        } else {
          ElMessage.error(res.message || "图片插入失败");
        }
      },
    },
  },
};

const handleEditorCreated = (editor) => {
  editorRef.value = editor;
};
onBeforeUnmount(() => {
  if (editorRef.value) editorRef.value.destroy();
});

// 业务逻辑...
const loadArticles = async () => {
  loading.value = true;
  try {
    const res = await fetchArticlePage({
      pageNum: currentPage.value,
      pageSize,
      keyword: keyword.value,
      status: status.value,
    });
    articles.value = res.data.records;
    total.value = res.data.total;
  } finally {
    loading.value = false;
  }
};

const handlePageChange = (page) => {
  currentPage.value = page;
  loadArticles();
};

const handleSelectionChange = (val) => {
  multipleSelection.value = val.map((i) => i.id);
};

const openDialog = (row = null) => {
  if (row) {
    dialogTitle.value = "编辑文章";
    form.value = { ...row };
    editId.value = row.id;
  } else {
    dialogTitle.value = "新增文章";
    form.value = { title: "", author: "", status: "DRAFT", content: "" };
    editId.value = null;
  }
  dialogVisible.value = true;
};

const submitForm = async () => {
  if (editId.value) {
    await updateArticle({ ...form.value, id: editId.value });
  } else {
    await addArticle(form.value);
  }
  dialogVisible.value = false;
  loadArticles();
};

const deleteSingle = async (id) => {
  await deleteArticle(id);
  loadArticles();
};

const handleBatchDelete = async () => {
  await deleteArticles(multipleSelection.value);
  loadArticles();
};

const toggleStatus = async (row) => {
  const newStatus = row.status === "PUBLISHED" ? "DRAFT" : "PUBLISHED";
  try {
    await updateArticleStatus(row.id, newStatus);
    ElMessage.success(
      `已成功设置为${newStatus === "PUBLISHED" ? "发布" : "草稿"}`
    );
    loadArticles();
  } catch {
    ElMessage.error("状态更新失败");
  }
};

const resetFilters = () => {
  keyword.value = "";
  status.value = "";
  loadArticles();
};

onMounted(() => {
  loadArticles();
});
</script>

<style scoped>
.admin-article {
  padding: 0; /* 移除内边距，因为父容器已有padding */
}
.pagination {
  margin-top: 20px;
  text-align: right;
}
.dialog-footer {
  text-align: right;
}
.el-form {
  padding-right: 10px;
}
.el-form-item {
  margin-bottom: 20px;
}
</style>
