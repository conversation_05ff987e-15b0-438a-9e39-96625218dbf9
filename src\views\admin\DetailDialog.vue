<template>
    <el-dialog v-model="dialogVisible" title="非遗项目资源详情" width="80%" top="5vh">
     <!-- 一、项目基本信息 -->
      <div class="section">
        <h3>一、项目基本信息</h3>
  
        <!-- 列入各级名录情况 -->
        <h4>列入各级名录情况</h4>
        <el-collapse accordion>
          <el-collapse-item title="国家级" name="1">
            <el-descriptions border column="2">
              <el-descriptions-item label="项目名称">{{ details.inclusion_in_lists.national.name }}</el-descriptions-item>
              <el-descriptions-item label="类别">{{ details.inclusion_in_lists.national.category }}</el-descriptions-item>
              <el-descriptions-item label="编号">{{ details.inclusion_in_lists.national.code }}</el-descriptions-item>
              <el-descriptions-item label="申报地区或单位">{{ details.inclusion_in_lists.national.declaring_region_or_unit }}</el-descriptions-item>
              <el-descriptions-item label="列入时间">{{ details.inclusion_in_lists.national.inclusion_time }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item title="省级" name="2">
            <el-descriptions border column="2">
              <el-descriptions-item label="项目名称">{{ details.inclusion_in_lists.provincial.name }}</el-descriptions-item>
              <el-descriptions-item label="类别">{{ details.inclusion_in_lists.provincial.category }}</el-descriptions-item>
              <el-descriptions-item label="编号">{{ details.inclusion_in_lists.provincial.code }}</el-descriptions-item>
              <el-descriptions-item label="申报地区或单位">{{ details.inclusion_in_lists.provincial.declaring_region_or_unit }}</el-descriptions-item>
              <el-descriptions-item label="列入时间">{{ details.inclusion_in_lists.provincial.inclusion_time }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item title="市级" name="3">
            <el-descriptions border column="2">
              <el-descriptions-item label="项目名称">{{ details.inclusion_in_lists.municipal.name }}</el-descriptions-item>
              <el-descriptions-item label="类别">{{ details.inclusion_in_lists.municipal.category }}</el-descriptions-item>
              <el-descriptions-item label="编号">{{ details.inclusion_in_lists.municipal.code }}</el-descriptions-item>
              <el-descriptions-item label="申报地区或单位">{{ details.inclusion_in_lists.municipal.declaring_region_or_unit }}</el-descriptions-item>
              <el-descriptions-item label="列入时间">{{ details.inclusion_in_lists.municipal.inclusion_time }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item title="县级" name="4">
            <el-descriptions border column="2">
              <el-descriptions-item label="项目名称">{{ details.inclusion_in_lists.county.name }}</el-descriptions-item>
              <el-descriptions-item label="类别">{{ details.inclusion_in_lists.county.category }}</el-descriptions-item>
              <el-descriptions-item label="编号">{{ details.inclusion_in_lists.county.code }}</el-descriptions-item>
              <el-descriptions-item label="申报地区或单位">{{ details.inclusion_in_lists.county.declaring_region_or_unit }}</el-descriptions-item>
              <el-descriptions-item label="列入时间">{{ details.inclusion_in_lists.county.inclusion_time }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
  
  <!-- 涉及民族 -->
        <h4>涉及民族</h4>
        <el-descriptions border column="1">
          <el-descriptions-item label="涉及民族">{{ details.involved_ethnic_groups || '暂无' }}</el-descriptions-item>
        </el-descriptions>
  
        <!-- 地理位置及分布区域 -->
        <h4>地理位置及分布区域</h4>
        <el-descriptions border column="1">
          <el-descriptions-item label="地理位置及分布区域">{{ details.geographical_location_and_distribution_area }}</el-descriptions-item>
        </el-descriptions>
  
        <!-- 项目简介 -->
        <h4>项目简介</h4>
        <el-descriptions border column="1">
          <el-descriptions-item label="项目简介">{{ details.project_introduction }}</el-descriptions-item>
        </el-descriptions>
  
        <!-- 传承脉络 -->
        <h4>传承脉络</h4>
        <el-descriptions border column="1">
          <el-descriptions-item label="传承脉络">{{ details.inheritance_context }}</el-descriptions-item>
        </el-descriptions>
  
        <!-- 社会功能和文化意义 -->
        <h4>社会功能和文化意义</h4>
        <el-descriptions border column="1">
          <el-descriptions-item label="社会功能和文化意义">{{ details.social_function_and_cultural_significance }}</el-descriptions-item>
        </el-descriptions>
  
        <!-- 保护单位情况 -->
        <h4 style="margin-top: 16px">保护单位情况</h4>
        <el-descriptions border column="2">
          <el-descriptions-item label="单位名称">{{ details.protection_unit_info.unit_name }}</el-descriptions-item>
          <el-descriptions-item label="法人类型">
            <el-select v-model="details.protection_unit_info.legal_person_type" disabled>
              <el-option label="事业单位法人" value="事业单位法人" />
              <el-option label="社会团体法人" value="社会团体法人" />
              <el-option label="企业法人" value="企业法人" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
  
        <!-- 近五年来所采取的保护措施及其成效 -->
        <h4 style="margin-top: 16px">近五年来所采取的保护措施及其成效</h4>
        <p>{{ details.protection_measures_and_effectiveness }}</p>
  
        <!-- 近五年投入项目保护资金 -->
        <h4 style="margin-top: 16px">近五年投入项目保护资金</h4>
        <el-descriptions border column="2">
          <el-descriptions-item label="资金总量">{{ details.funding_for_protection.total_amount }}</el-descriptions-item>
          <el-descriptions-item label="资金投入变化情况">
            <el-select v-model="details.funding_for_protection.change_in_funding" disabled>
              <el-option label="维持" value="maintained" />
              <el-option label="递增" value="increased" />
              <el-option label="减少" value="decreased" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="资金来源">
            <el-checkbox-group v-model="details.funding_for_protection.funding_sources" disabled>
              <el-checkbox label="政府投入" value="government_input" />
              <el-checkbox label="市场营收" value="market_revenue" />
              <el-checkbox label="社会捐赠" value="social_donations" />
              <el-checkbox label="其他" value="others" />
            </el-checkbox-group>
          </el-descriptions-item>
        </el-descriptions>
      </div>
  
  <!-- 二、项目存续情况 -->
      <div class="section" style="margin-top: 24px">
        <h3>二、项目存续情况</h3>
        <el-descriptions border column="2">
          <el-descriptions-item label="是否有固定的实践活动场所">{{ details.practice_situation.has_fixed_practice_venue ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="实践活动的频次变化情况">
            <el-select v-model="details.practice_situation.practice_frequency_change" disabled>
              <el-option label="维持" value="maintained" />
              <el-option label="扩大" value="expanded" />
              <el-option label="缩小" value="shrunk" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="基本实践方式是否得以维持">{{ details.practice_situation.basic_practice_method_maintained ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="是否有新的创造或发展">{{ details.practice_situation.has_new_creation_or_development ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="受众人群规模的变化情况">
            <el-select v-model="details.practice_situation.audience_size_change" disabled>
              <el-option label="维持" value="maintained" />
              <el-option label="扩大" value="expanded" />
              <el-option label="缩小" value="shrunk" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="相关收入的变化情况">
            <el-select v-model="details.practice_situation.related_income_change" disabled>
              <el-option label="维持" value="maintained" />
              <el-option label="增加" value="increased" />
              <el-option label="减少" value="decreased" />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
  
        <!-- 传承情况 -->
        <h4 style="margin-top: 16px">传承情况</h4>
        <el-descriptions border column="2">
          <el-descriptions-item label="国家级代表性传承人">{{ details.inheritance_status.representative_inheritors_at_all_levels.national }}</el-descriptions-item>
          <el-descriptions-item label="省级代表性传承人">{{ details.inheritance_status.representative_inheritors_at_all_levels.provincial }}</el-descriptions-item>
          <el-descriptions-item label="市级代表性传承人">{{ details.inheritance_status.representative_inheritors_at_all_levels.municipal }}</el-descriptions-item>
          <el-descriptions-item label="县级代表性传承人">{{ details.inheritance_status.representative_inheritors_at_all_levels.county }}</el-descriptions-item>
          <el-descriptions-item label="各级代表性传承人收徒数量变化情况">
            <el-select v-model="details.inheritance_status.change_in_number_of_apprentices_of_representative_inheritors" disabled>
              <el-option label="维持" value="maintained" />
              <el-option label="增加" value="increased" />
              <el-option label="减少" value="decreased" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="传承人群规模的变化情况">
            <el-select v-model="details.inheritance_status.change_in_size_of_inheritance_group" disabled>
              <el-option label="维持" value="maintained" />
              <el-option label="扩大" value="expanded" />
              <el-option label="缩小" value="shrunk" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="传承人群的年龄结构情况">
            <el-select v-model="details.inheritance_status.age_structure_of_inheritance_group" disabled>
              <el-option label="老中青结合，年龄结构合理" value="reasonable_mix" />
              <el-option label="中老年居多，45岁以下偏少" value="mostly_middle_aged_and_elderly" />
              <el-option label="老龄化严重，中青年后继乏人" value="serious_aging" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="是否有用于开展项目传承活动的场所">{{ details.inheritance_status.has_venue_for_inheritance_activities ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="是否开展传承人群培训活动">{{ details.inheritance_status.inheritance_group_capacity_building.has_training_activities ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="培训内容">
            <el-checkbox-group v-model="details.inheritance_status.inheritance_group_capacity_building.training_activity_types" disabled>
              <el-checkbox label="知识技能培训" value="knowledge_and_skill_training" />
              <el-checkbox label="保护理念与基本知识培训" value="protection_concept_and_basic_knowledge_training" />
              <el-checkbox label="展览展示与媒体传播培训" value="exhibition_and_media_training" />
            </el-checkbox-group>
          </el-descriptions-item>
        </el-descriptions>
   <!-- 宣传展示情况 -->
        <h4 style="margin-top: 16px">宣传展示情况</h4>
        <el-descriptions border column="2">
          <el-descriptions-item label="是否开展宣传展示活动">{{ details.promotion_and_display_status.has_promotion_and_display_activities ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="宣传活动规模">
            <el-checkbox-group v-model="details.promotion_and_display_status.activity_scales" disabled>
              <el-checkbox label="本地区" value="本地区" />
              <el-checkbox label="本省" value="本省" />
              <el-checkbox label="跨省" value="跨省" />
              <el-checkbox label="全国性" value="全国性" />
              <el-checkbox label="国际性" value="国际性" />
            </el-checkbox-group>
          </el-descriptions-item>
          <el-descriptions-item label="活动频次变化">
            <el-select v-model="details.promotion_and_display_status.frequency_change_of_activities" disabled>
              <el-option label="维持" value="maintained" />
              <el-option label="增加" value="expanded" />
              <el-option label="减少" value="shrunk" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="新闻媒体关注度">
            <el-select v-model="details.promotion_and_display_status.media_attention" disabled>
              <el-option label="较高" value="high" />
              <el-option label="一般" value="medium" />
              <el-option label="较差" value="low" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="是否进入中小学课程体系">{{ details.promotion_and_display_status.into_school_situation.included_in_curriculum ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="是否纳入教材">{{ details.promotion_and_display_status.into_school_situation.included_in_textbooks ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="是否开展校园活动">{{ details.promotion_and_display_status.into_school_situation.has_campus_activities ? '是' : '否' }}</el-descriptions-item>
        </el-descriptions>
  
        <!-- 相关实物及场所的现状 -->
        <h4 style="margin-top: 16px">相关实物及场所的现状</h4>
        <el-descriptions border column="1">
          <el-descriptions-item label="项目相关实物和场所是否得到有效保护">{{ details.status_of_related_artifacts_and_sites ? '是' : '否' }}</el-descriptions-item>
        </el-descriptions>
  
        <!-- 项目传承发展的主要威胁 -->
        <h4 style="margin-top: 16px">项目传承发展的主要威胁</h4>
        <p style="white-space: pre-line">{{ details.main_threats_to_inheritance_and_development }}</p>
  
        <!-- 项目存续总体状况 -->
        <h4 style="margin-top: 16px">项目存续总体状况</h4>
        <el-select v-model="details.overall_survival_status" disabled>
          <el-option label="好" value="good" />
          <el-option label="良好" value="fair" />
          <el-option label="一般" value="average" />
          <el-option label="急需保护" value="urgent_protection_needed" />
        </el-select>
      </div>
  
      <!-- 三、下一步保护计划 -->
      <div class="section" style="margin-top: 24px">
        <h3>三、下一步保护计划</h3>
        <p style="white-space: pre-line">{{ details.future_protection_plan }}</p>
      </div>
  
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </template>
  <script setup>
 import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  details: { type: Object, required: true },
  visible: { type: Boolean, required: true },
});
const emit = defineEmits(['update:visible']);

const close = () => emit('update:visible', false);
  </script>
  
  <style scoped>
  .section {
    margin-bottom: 20px;
  }
  </style>