<template>
  <div
    class="project-detail"
    style="margin-top: 0px; background-color: #a89171"
  >
    <h2 style="text-align: center">
      {{ projectDetail.name || "暂无项目名称" }}
    </h2>
    <!-- 基础信息居中 -->
    <div class="basic-info">
      <p>级别：{{ projectDetail.projectLevel || "暂无" }}</p>
      <p>类别：{{ projectDetail.category || "暂无" }}</p>
      <p>地区：{{ projectDetail.region || "暂无" }}</p>
      <p>继承人：{{ projectDetail.inheritor || "暂无继承人信息" }}</p>
      <p>涉及民族：{{ projectDetail.nationalities || "暂无信息" }}</p>
    </div>

    <!-- 切换按钮 -->
    <div class="resource-toggle">
      <button @click="filterType = 1">图片资源</button>
      <button @click="filterType = 0">视频资源</button>
      <button @click="filterType = 2">音频资源</button>
    </div>

    <!-- 资源展示 -->
    <div class="main-resource">
      <div v-if="filteredResources.length > 0 && currentResource.resourceUrl">
        <div v-if="filterType === 1" class="image-slider">
          <button
            @click="prevResource"
            class="nav-button"
            v-show="filteredResources.length > 1"
          >⬅️</button>
          <img
            :src="currentResource.resourceUrl"
            alt="项目图片"
            class="resource-image"
          />
          <button
            @click="nextResource"
            class="nav-button"
            v-show="filteredResources.length > 1"
          >➡️</button>
        </div>
        <div v-else-if="filterType === 0" class="video-player">
          <button
            @click="prevResource"
            class="nav-button"
            v-show="filteredResources.length > 1"
          >⬅️</button>
          <video
            :src="currentResource.resourceUrl"
            controls
            class="resource-video"
          />
          <button
            @click="nextResource"
            class="nav-button"
            v-show="filteredResources.length > 1"
          >➡️</button>
        </div>
        <div v-else-if="filterType === 2" class="audio-player">
          <button
            @click="prevResource"
            class="nav-button"
            v-show="filteredResources.length > 1"
          >⬅️</button>
          <audio
            :src="currentResource.resourceUrl"
            controls
            class="resource-audio"
          />
          <button
            @click="nextResource"
            class="nav-button"
            v-show="filteredResources.length > 1"
          >➡️</button>
        </div>

        <!-- 资源计数显示 -->
        <div v-if="filteredResources.length > 1" class="resource-counter">
          {{ currentIndex + 1 }} / {{ filteredResources.length }}
        </div>

        <p class="resource-description">
          {{ currentResource.description || "暂无资源描述" }}
        </p>
      </div>
      <div v-else style="text-align: center">暂无资源</div>
    </div>

    <!-- 详细信息展示 -->
    <div class="project-info-block">
      <div class="info-card">
        <h3>简介</h3>
        <p>{{ detailInfo.project_introduction || "暂无信息" }}</p>
      </div>

      <div class="info-card">
        <h3>地理分布</h3>
        <p>
          {{
            detailInfo.geographical_location_and_distribution_area || "暂无信息"
          }}
        </p>
      </div>

      <div class="info-card">
        <h3>传承脉络</h3>
        <p>{{ detailInfo.inheritance_context || "暂无信息" }}</p>
      </div>

      <div class="info-card">
        <h3>面临威胁</h3>
        <p>
          {{
            detailInfo.main_threats_to_inheritance_and_development || "暂无信息"
          }}
        </p>
      </div>

      <div class="info-card">
        <h3>未来保护计划</h3>
        <p>{{ detailInfo.future_protection_plan || "暂无信息" }}</p>
      </div>
    </div>
    <!-- 只改按钮这一行，其余保持 -->
    <button @click="goBack" class="back-button">⬅ 返回列表</button>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, watchEffect } from "vue";
import { useRoute, useRouter } from "vue-router";
import { fetchHeritageProjectDetail } from "@/api/heritageProject";

const route = useRoute();
const router = useRouter();
const id = route.params.id;

const projectDetail = ref({});
const resourceList = ref([]);
const detailInfo = ref({});
const filterType = ref(1); // 默认展示图片资源

const filteredResources = computed(() =>
  resourceList.value.filter((item) => item.type === filterType.value)
);
const currentIndex = ref(0);
const currentResource = computed(
  () => filteredResources.value[currentIndex.value] || {}
);

const prevResource = () => {
  const len = filteredResources.value.length;
  currentIndex.value = (currentIndex.value - 1 + len) % len;
};
const nextResource = () => {
  const len = filteredResources.value.length;
  currentIndex.value = (currentIndex.value + 1) % len;
};
const goBack = () => {
  router.push({
    name: "资源",
    query: route.query, // 直接使用当前路由的 query
  });
};

watch(filterType, () => {
  currentIndex.value = 0;
});

const fetchProjectDetail = async () => {
  try {
    const res = await fetchHeritageProjectDetail(id);
    const data = res.data?.data || res?.data;

    if (data) {
      projectDetail.value = data;
      resourceList.value = data.resourceList || [];
      detailInfo.value = data.details || {};
    } else {
      console.error("❌ 接口响应失败：", res?.data?.message || "未知错误");
    }
  } catch (error) {
    console.error("❌ 接口请求异常：", error);
  }
};

onMounted(fetchProjectDetail);

watchEffect(() => {
  console.log("当前资源列表：", resourceList.value);
});
</script>

<style scoped>
.project-detail {
  position: relative; /* 让子元素 absolute 以此为参照 */
  padding: 40px;
  color: #333;
  background: #a89171;
}

/* 美化 + 顶部定位 */
.back-button {
  position: fixed; /* 相对于浏览器窗口固定 */
  right: 120px; /* 视口右边距 */
  bottom: 40px; /* 视口下边距 */
  background: #20b2aa;
  color: #fff;
  padding: 14px 36px; /* 比原先再大一点手感更好 */
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: 30px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: background 0.25s, transform 0.15s;
}

.back-button:hover {
  background: #1a998e;
  transform: translateY(-2px);
}

.basic-info {
  text-align: center;
  line-height: 1.8;
  margin-bottom: 20px;
}

.resource-toggle {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 20px 0;
}
.resource-toggle button {
  background-color: #e0c9a6;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 8px;
  font-weight: bold;
}
.resource-toggle button:hover {
  background-color: #d1b38a;
}

.main-resource {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
}

.image-slider,
.video-player,
.audio-player {
  display: flex;
  align-items: center;
  justify-content: center;
}

.resource-image,
.resource-video {
  width: 100%;
  max-width: 800px;
  max-height: 600px;
  border-radius: 8px;
}

.resource-audio {
  width: 100%;
  max-width: 600px;
  margin: 0 20px;
}

.resource-counter {
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5px 15px;
  border-radius: 15px;
  display: inline-block;
}

.resource-description {
  text-align: center;
  margin-top: 10px;
  font-style: italic;
  color: #555;
}

.nav-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #333;
  padding: 0 10px;
}

.project-info-block {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 30px;
  max-width: 90%; /* 默认比屏宽收 10% */
  margin: 30px auto 60px;
}

.info-card {
  background-color: #f9f5f0;
  padding: 20px 30px;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 大屏再收一点，阅读更舒适 */
@media (min-width: 1200px) {
  .project-info-block {
    max-width: 70%;
  }
}

/* info 卡片之间留足空隙 */
.info-card:not(:last-child) {
  margin-bottom: 24px;
}

.info-card h3 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  border-left: 4px solid #a89171;
  padding-left: 10px;
  color: #5a3e2b;
}

.info-card p {
  font-size: 16px;
  line-height: 1.8;
  text-align: justify;
  color: #333;
}
</style>
