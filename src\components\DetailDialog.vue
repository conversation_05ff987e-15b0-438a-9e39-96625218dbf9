<template>
  <el-dialog
    v-if="dialogVisible"
    v-model="dialogVisible"
    :title="editable ? '编辑项目详情' : '项目详情'"
    width="80%"
    top="5vh"
    @close="handleClose"
  >
    <!-- 一、项目基本信息 -->
    <div class="section">
      <h3>一、项目基本信息</h3>

      <h4>列入各级名录情况</h4>
      <el-collapse accordion>
        <el-collapse-item title="国家级" name="1">
          <el-descriptions border column="2">
            <el-descriptions-item label="项目名称">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.national.name"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.national.name || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="类别">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.national.category"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.national.category || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="编号">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.national.code"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.national.code || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="申报地区或单位">
              <template v-if="editable">
                <el-input
                  v-model="
                    localDetails.inclusion_in_lists.national
                      .declaring_region_or_unit
                  "
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.national
                  .declaring_region_or_unit || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="列入时间">
              <template v-if="editable">
                <el-input
                  v-model="
                    localDetails.inclusion_in_lists.national.inclusion_time
                  "
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.national.inclusion_time ||
                "暂无"
              }}</template>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>

        <el-collapse-item title="省级" name="2">
          <el-descriptions border column="2">
            <el-descriptions-item label="项目名称">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.provincial.name"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.provincial.name || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="类别">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.provincial.category"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.provincial.category || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="编号">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.provincial.code"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.provincial.code || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="申报地区或单位">
              <template v-if="editable">
                <el-input
                  v-model="
                    localDetails.inclusion_in_lists.provincial
                      .declaring_region_or_unit
                  "
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.provincial
                  .declaring_region_or_unit || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="列入时间">
              <template v-if="editable">
                <el-input
                  v-model="
                    localDetails.inclusion_in_lists.provincial.inclusion_time
                  "
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.provincial.inclusion_time ||
                "暂无"
              }}</template>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>

        <el-collapse-item title="市级" name="3">
          <el-descriptions border column="2">
            <el-descriptions-item label="项目名称">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.municipal.name"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.municipal.name || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="类别">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.municipal.category"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.municipal.category || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="编号">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.municipal.code"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.municipal.code || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="申报地区或单位">
              <template v-if="editable">
                <el-input
                  v-model="
                    localDetails.inclusion_in_lists.municipal
                      .declaring_region_or_unit
                  "
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.municipal
                  .declaring_region_or_unit || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="列入时间">
              <template v-if="editable">
                <el-input
                  v-model="
                    localDetails.inclusion_in_lists.municipal.inclusion_time
                  "
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.municipal.inclusion_time ||
                "暂无"
              }}</template>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>

        <el-collapse-item title="县级" name="4">
          <el-descriptions border column="2">
            <el-descriptions-item label="项目名称">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.county.name"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.county.name || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="类别">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.county.category"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.county.category || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="编号">
              <template v-if="editable">
                <el-input
                  v-model="localDetails.inclusion_in_lists.county.code"
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.county.code || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="申报地区或单位">
              <template v-if="editable">
                <el-input
                  v-model="
                    localDetails.inclusion_in_lists.county
                      .declaring_region_or_unit
                  "
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.county
                  .declaring_region_or_unit || "暂无"
              }}</template>
            </el-descriptions-item>
            <el-descriptions-item label="列入时间">
              <template v-if="editable">
                <el-input
                  v-model="
                    localDetails.inclusion_in_lists.county.inclusion_time
                  "
                />
              </template>
              <template v-else>{{
                localDetails.inclusion_in_lists.county.inclusion_time || "暂无"
              }}</template>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
      </el-collapse>

      <el-descriptions
        border
        column="1"
        label-style="width: 80px"
        class="uniform-descriptions"
      >
        <el-descriptions-item label="涉及民族">
          <template v-if="editable">
            <el-input
              type="textarea"
              :rows="3"
              v-model="localDetails.involved_ethnic_groups"
            />
          </template>
          <template v-else>
            <div class="readonly-text">
              {{ localDetails.involved_ethnic_groups || "暂无" }}
            </div>
          </template>
        </el-descriptions-item>

        <el-descriptions-item label="地理位置及分布区域">
          <template v-if="editable">
            <el-input
              type="textarea"
              :rows="3"
              v-model="localDetails.geographical_location_and_distribution_area"
            />
          </template>
          <template v-else>
            <div class="readonly-text">
              {{
                localDetails.geographical_location_and_distribution_area ||
                "暂无"
              }}
            </div>
          </template>
        </el-descriptions-item>

        <el-descriptions-item label="项目简介">
          <template v-if="editable">
            <el-input
              type="textarea"
              :rows="3"
              v-model="localDetails.project_introduction"
            />
          </template>
          <template v-else>
            <div class="readonly-text">
              {{ localDetails.project_introduction || "暂无" }}
            </div>
          </template>
        </el-descriptions-item>

        <el-descriptions-item label="传承脉络">
          <template v-if="editable">
            <el-input
              type="textarea"
              :rows="3"
              v-model="localDetails.inheritance_context"
            />
          </template>
          <template v-else>
            <div class="readonly-text">
              {{ localDetails.inheritance_context || "暂无" }}
            </div>
          </template>
        </el-descriptions-item>

        <el-descriptions-item label="社会功能和文化意义">
          <template v-if="editable">
            <el-input
              type="textarea"
              :rows="3"
              v-model="localDetails.social_function_and_cultural_significance"
            />
          </template>
          <template v-else>
            <div class="readonly-text">
              {{
                localDetails.social_function_and_cultural_significance || "暂无"
              }}
            </div>
          </template>
        </el-descriptions-item>
      </el-descriptions>

      <h4 style="margin-top: 16px">保护单位情况</h4>
      <el-descriptions border column="2">
        <el-descriptions-item label="单位名称">
          <template v-if="editable">
            <el-input v-model="localDetails.protection_unit_info.unit_name" />
          </template>
          <template v-else>{{
            localDetails.protection_unit_info.unit_name || "暂无"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="法人类型">
          <template v-if="editable">
            <el-select
              v-model="localDetails.protection_unit_info.legal_person_type"
              placeholder="请选择"
            >
              <el-option label="事业单位法人" value="事业单位法人" />
              <el-option label="社会团体法人" value="社会团体法人" />
              <el-option label="企业法人" value="企业法人" />
              <el-option label="其他" value="其他" />
            </el-select>
          </template>
          <template v-else>
            <el-select
              v-model="localDetails.protection_unit_info.legal_person_type"
              disabled
            >
              <el-option label="事业单位法人" value="事业单位法人" />
              <el-option label="社会团体法人" value="社会团体法人" />
              <el-option label="企业法人" value="企业法人" />
              <el-option label="其他" value="其他" />
            </el-select>
          </template>
        </el-descriptions-item>
      </el-descriptions>

      <h4 style="margin-top: 16px">近五年来所采取的保护措施及其成效</h4>
      <p>
        <template v-if="editable">
          <el-input
            type="textarea"
            rows="3"
            v-model="localDetails.protection_measures_and_effectiveness"
          />
        </template>
        <template v-else>{{
          localDetails.protection_measures_and_effectiveness || "暂无"
        }}</template>
      </p>

      <h4 style="margin-top: 16px">近五年投入项目保护资金</h4>
      <el-descriptions border column="2">
        <el-descriptions-item label="资金总量">
          <template v-if="editable">
            <el-input
              v-model="localDetails.funding_for_protection.total_amount"
            />
          </template>
          <template v-else>{{
            localDetails.funding_for_protection.total_amount || "暂无"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="资金投入变化情况">
          <template v-if="editable">
            <el-select
              v-model="localDetails.funding_for_protection.change_in_funding"
              placeholder="请选择"
            >
              <el-option label="维持" value="maintained" />
              <el-option label="递增" value="increased" />
              <el-option label="减少" value="decreased" />
            </el-select>
          </template>
          <template v-else>
            <el-select
              v-model="localDetails.funding_for_protection.change_in_funding"
              disabled
            >
              <el-option label="维持" value="maintained" />
              <el-option label="递增" value="increased" />
              <el-option label="减少" value="decreased" />
            </el-select>
          </template>
        </el-descriptions-item>
        <el-descriptions-item label="资金来源">
          <template v-if="editable">
            <el-checkbox-group
              v-model="localDetails.funding_for_protection.funding_sources"
            >
              <el-checkbox label="government_input">政府投入</el-checkbox>
              <el-checkbox label="market_revenue">市场营收</el-checkbox>
              <el-checkbox label="social_donations">社会捐赠</el-checkbox>
              <el-checkbox label="others">其他</el-checkbox>
            </el-checkbox-group>
          </template>
          <template v-else>
            <div>
              {{
                (localDetails.funding_for_protection.funding_sources || [])
                  .map(
                    (item) =>
                      ({
                        government_input: "政府投入",
                        market_revenue: "市场营收",
                        social_donations: "社会捐赠",
                        others: "其他",
                      }[item] || item)
                  )
                  .join("，") || "暂无"
              }}
            </div>
          </template>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 二、项目存续情况 -->
    <div class="section" style="margin-top: 24px">
      <h3>二、项目存续情况</h3>
      <h4 style="margin-top: 16px">实践情况</h4>
      <el-descriptions border column="2">
        <el-descriptions-item label="是否有固定的实践活动场所">
          <template v-if="editable">
            <el-switch
              v-model="localDetails.practice_situation.has_fixed_practice_venue"
            />
          </template>
          <template v-else>{{
            localDetails.practice_situation.has_fixed_practice_venue
              ? "是"
              : "否"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="实践活动的频次变化情况">
          <template v-if="editable">
            <el-select
              v-model="
                localDetails.practice_situation.practice_frequency_change
              "
              placeholder="请选择"
            >
              <el-option label="维持" value="maintained" />
              <el-option label="扩大" value="expanded" />
              <el-option label="缩小" value="shrunk" />
            </el-select>
          </template>
          <template v-else>{{
            localDetails.practice_situation.practice_frequency_change || "暂无"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="基本实践方式是否得以维持">
          <template v-if="editable">
            <el-switch
              v-model="
                localDetails.practice_situation.basic_practice_method_maintained
              "
            />
          </template>
          <template v-else>{{
            localDetails.practice_situation.basic_practice_method_maintained
              ? "是"
              : "否"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="是否有新的创造或发展">
          <template v-if="editable">
            <el-switch
              v-model="
                localDetails.practice_situation.has_new_creation_or_development
              "
            />
          </template>
          <template v-else>{{
            localDetails.practice_situation.has_new_creation_or_development
              ? "是"
              : "否"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="受众人群规模的变化情况">
          <template v-if="editable">
            <el-select
              v-model="localDetails.practice_situation.audience_size_change"
              placeholder="请选择"
            >
              <el-option label="维持" value="maintained" />
              <el-option label="扩大" value="expanded" />
              <el-option label="缩小" value="shrunk" />
            </el-select>
          </template>
          <template v-else>{{
            localDetails.practice_situation.audience_size_change || "暂无"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="相关收入的变化情况">
          <template v-if="editable">
            <el-select
              v-model="localDetails.practice_situation.related_income_change"
              placeholder="请选择"
            >
              <el-option label="维持" value="maintained" />
              <el-option label="增加" value="increased" />
              <el-option label="减少" value="decreased" />
            </el-select>
          </template>
          <template v-else>{{
            localDetails.practice_situation.related_income_change || "暂无"
          }}</template>
        </el-descriptions-item>
      </el-descriptions>

      <h4 style="margin-top: 16px">传承情况</h4>
      <el-descriptions border column="2">
        <el-descriptions-item label="国家级代表性传承人">
          <template v-if="editable">
            <el-input-number
              v-model="
                localDetails.inheritance_status
                  .representative_inheritors_at_all_levels.national
              "
              :min="0"
            />
          </template>
          <template v-else>{{
            localDetails.inheritance_status
              .representative_inheritors_at_all_levels.national || 0
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="省级代表性传承人">
          <template v-if="editable">
            <el-input-number
              v-model="
                localDetails.inheritance_status
                  .representative_inheritors_at_all_levels.provincial
              "
              :min="0"
            />
          </template>
          <template v-else>{{
            localDetails.inheritance_status
              .representative_inheritors_at_all_levels.provincial || 0
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="市级代表性传承人">
          <template v-if="editable">
            <el-input-number
              v-model="
                localDetails.inheritance_status
                  .representative_inheritors_at_all_levels.municipal
              "
              :min="0"
            />
          </template>
          <template v-else>{{
            localDetails.inheritance_status
              .representative_inheritors_at_all_levels.municipal || 0
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="县级代表性传承人">
          <template v-if="editable">
            <el-input-number
              v-model="
                localDetails.inheritance_status
                  .representative_inheritors_at_all_levels.county
              "
              :min="0"
            />
          </template>
          <template v-else>{{
            localDetails.inheritance_status
              .representative_inheritors_at_all_levels.county || 0
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="各级代表性传承人收徒数量变化情况">
          <template v-if="editable">
            <el-select
              v-model="
                localDetails.inheritance_status
                  .change_in_number_of_apprentices_of_representative_inheritors
              "
              placeholder="请选择"
            >
              <el-option label="维持" value="maintained" />
              <el-option label="增加" value="increased" />
              <el-option label="减少" value="decreased" />
            </el-select>
          </template>
          <template v-else>{{
            localDetails.inheritance_status
              .change_in_number_of_apprentices_of_representative_inheritors ||
            "暂无"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="传承人群规模的变化情况">
          <template v-if="editable">
            <el-select
              v-model="
                localDetails.inheritance_status
                  .change_in_size_of_inheritance_group
              "
              placeholder="请选择"
            >
              <el-option label="维持" value="maintained" />
              <el-option label="扩大" value="expanded" />
              <el-option label="缩小" value="shrunk" />
            </el-select>
          </template>
          <template v-else>{{
            localDetails.inheritance_status
              .change_in_size_of_inheritance_group || "暂无"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="传承人群的年龄结构情况">
          <template v-if="editable">
            <el-select
              v-model="
                localDetails.inheritance_status
                  .age_structure_of_inheritance_group
              "
              placeholder="请选择"
            >
              <el-option
                label="老中青结合，年龄结构合理"
                value="reasonable_mix"
              />
              <el-option
                label="中老年居多，45岁以下偏少"
                value="mostly_middle_aged_and_elderly"
              />
              <el-option
                label="老龄化严重，中青年后继乏人"
                value="serious_aging"
              />
            </el-select>
          </template>
          <template v-else>{{
            localDetails.inheritance_status
              .age_structure_of_inheritance_group || "暂无"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="是否有用于开展项目传承活动的场所">
          <template v-if="editable">
            <el-switch
              v-model="
                localDetails.inheritance_status
                  .has_venue_for_inheritance_activities
              "
            />
          </template>
          <template v-else>{{
            localDetails.inheritance_status.has_venue_for_inheritance_activities
              ? "是"
              : "否"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="是否开展传承人群培训活动">
          <template v-if="editable">
            <el-switch
              v-model="
                localDetails.inheritance_status
                  .inheritance_group_capacity_building.has_training_activities
              "
            />
          </template>
          <template v-else>{{
            localDetails.inheritance_status.inheritance_group_capacity_building
              .has_training_activities
              ? "是"
              : "否"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="培训内容">
          <template v-if="editable">
            <el-checkbox-group
              v-model="
                localDetails.inheritance_status
                  .inheritance_group_capacity_building.training_activity_types
              "
            >
              <el-checkbox
                label="知识技能培训"
                value="knowledge_and_skill_training"
              />
              <el-checkbox
                label="保护理念与基本知识培训"
                value="protection_concept_and_basic_knowledge_training"
              />
              <el-checkbox
                label="展览展示与媒体传播培训"
                value="exhibition_and_media_training"
              />
            </el-checkbox-group>
          </template>
          <template v-else>
            <span
              v-if="
                localDetails.inheritance_status
                  .inheritance_group_capacity_building.training_activity_types
                  .length
              "
            >
              {{
                localDetails.inheritance_status.inheritance_group_capacity_building.training_activity_types.join(
                  "，"
                )
              }}
            </span>
            <span v-else>暂无</span>
          </template>
        </el-descriptions-item>
      </el-descriptions>

      <h4 style="margin-top: 16px">宣传展示情况</h4>
      <el-descriptions border column="2">
        <el-descriptions-item label="是否开展宣传展示活动">
          <template v-if="editable">
            <el-switch
              v-model="
                localDetails.promotion_and_display_status
                  .has_promotion_and_display_activities
              "
            />
          </template>
          <template v-else>{{
            localDetails.promotion_and_display_status
              .has_promotion_and_display_activities
              ? "是"
              : "否"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="宣传活动规模">
          <template v-if="editable">
            <el-checkbox-group
              v-model="
                localDetails.promotion_and_display_status.activity_scales
              "
            >
              <el-checkbox label="本地区" value="本地区" />
              <el-checkbox label="本省" value="本省" />
              <el-checkbox label="跨省" value="跨省" />
              <el-checkbox label="全国性" value="全国性" />
              <el-checkbox label="国际性" value="国际性" />
            </el-checkbox-group>
          </template>
          <template v-else>
            <span
              v-if="
                localDetails.promotion_and_display_status.activity_scales.length
              "
            >
              {{
                localDetails.promotion_and_display_status.activity_scales.join(
                  "，"
                )
              }}
            </span>
            <span v-else>暂无</span>
          </template>
        </el-descriptions-item>
        <el-descriptions-item label="活动频次变化">
          <template v-if="editable">
            <el-select
              v-model="
                localDetails.promotion_and_display_status
                  .frequency_change_of_activities
              "
              placeholder="请选择"
            >
              <el-option label="维持" value="maintained" />
              <el-option label="增加" value="expanded" />
              <el-option label="减少" value="shrunk" />
            </el-select>
          </template>
          <template v-else>{{
            localDetails.promotion_and_display_status
              .frequency_change_of_activities || "暂无"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="新闻媒体关注度">
          <template v-if="editable">
            <el-select
              v-model="
                localDetails.promotion_and_display_status.media_attention
              "
              placeholder="请选择"
            >
              <el-option label="较高" value="high" />
              <el-option label="一般" value="medium" />
              <el-option label="较差" value="low" />
            </el-select>
          </template>
          <template v-else>{{
            localDetails.promotion_and_display_status.media_attention || "暂无"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="是否进入中小学课程体系">
          <template v-if="editable">
            <el-switch
              v-model="
                localDetails.promotion_and_display_status.into_school_situation
                  .included_in_curriculum
              "
            />
          </template>
          <template v-else>{{
            localDetails.promotion_and_display_status.into_school_situation
              .included_in_curriculum
              ? "是"
              : "否"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="是否纳入教材">
          <template v-if="editable">
            <el-switch
              v-model="
                localDetails.promotion_and_display_status.into_school_situation
                  .included_in_textbooks
              "
            />
          </template>
          <template v-else>{{
            localDetails.promotion_and_display_status.into_school_situation
              .included_in_textbooks
              ? "是"
              : "否"
          }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="是否开展校园活动">
          <template v-if="editable">
            <el-switch
              v-model="
                localDetails.promotion_and_display_status.into_school_situation
                  .has_campus_activities
              "
            />
          </template>
          <template v-else>{{
            localDetails.promotion_and_display_status.into_school_situation
              .has_campus_activities
              ? "是"
              : "否"
          }}</template>
        </el-descriptions-item>
      </el-descriptions>

      <h4 style="margin-top: 16px">相关实物及场所的现状</h4>
      <el-descriptions border column="1">
        <el-descriptions-item label="项目相关实物和场所是否得到有效保护">
          <template v-if="editable">
            <el-switch
              v-model="localDetails.status_of_related_artifacts_and_sites"
            />
          </template>
          <template v-else>{{
            localDetails.status_of_related_artifacts_and_sites ? "是" : "否"
          }}</template>
        </el-descriptions-item>
      </el-descriptions>

      <h4 style="margin-top: 16px">项目传承发展的主要威胁</h4>
      <p style="white-space: pre-line">
        <template v-if="editable">
          <el-input
            type="textarea"
            rows="4"
            v-model="localDetails.main_threats_to_inheritance_and_development"
          />
        </template>
        <template v-else>{{
          localDetails.main_threats_to_inheritance_and_development || "暂无"
        }}</template>
      </p>

      <h4 style="margin-top: 16px">项目存续总体状况</h4>
      <template v-if="editable">
        <el-select
          v-model="localDetails.overall_survival_status"
          placeholder="请选择"
        >
          <el-option label="好" value="good" />
          <el-option label="良好" value="fair" />
          <el-option label="一般" value="average" />
          <el-option label="急需保护" value="urgent_protection_needed" />
        </el-select>
      </template>
      <template v-else>
        <span>
          {{
            {
              good: "好",
              fair: "良好",
              average: "一般",
              urgent_protection_needed: "急需保护",
            }[localDetails.overall_survival_status] || "暂无"
          }}
        </span>
      </template>
    </div>

    <!-- 三、下一步保护计划 -->
    <div class="section" style="margin-top: 24px">
      <h3>三、下一步保护计划</h3>
      <p style="white-space: pre-line">
        <template v-if="editable">
          <el-input
            type="textarea"
            rows="4"
            v-model="localDetails.future_protection_plan"
          />
        </template>
        <template v-else>{{
          localDetails.future_protection_plan || "暂无"
        }}</template>
      </p>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button v-if="editable" type="primary" @click="handleSave"
        >确定</el-button
      >
      <el-button v-else @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from "vue";
import cloneDeep from "lodash/cloneDeep";

const props = defineProps({
  details: {
    type: Object,
    required: false,
    default: () => ({}),
  },
  visible: {
    type: Boolean,
    required: true,
  },
  editable: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:visible", "save"]);

const dialogVisible = ref(props.visible);
const localDetails = ref({});

// 递归初始化函数，给缺失字段补空对象或默认值
function initEmptyObject(obj, template) {
  if (!template || typeof template !== "object") return;
  if (!obj || typeof obj !== "object") return;

  for (const key in template) {
    if (!(key in obj) || obj[key] === null || obj[key] === undefined) {
      obj[key] =
        typeof template[key] === "object" && !Array.isArray(template[key])
          ? {}
          : cloneDeep(template[key]);
    }
    if (
      typeof template[key] === "object" &&
      !Array.isArray(template[key]) &&
      typeof obj[key] === "object"
    ) {
      initEmptyObject(obj[key], template[key]);
    }
  }
}

// 你的字段模板，结构与template里字段对应
const emptyDetailsTemplate = {
  project_name: "",
  inclusion_in_lists: {
    national: {
      name: "",
      category: "",
      code: "",
      declaring_region_or_unit: "",
      inclusion_time: "",
    },
    provincial: {
      name: "",
      category: "",
      code: "",
      declaring_region_or_unit: "",
      inclusion_time: "",
    },
    municipal: {
      name: "",
      category: "",
      code: "",
      declaring_region_or_unit: "",
      inclusion_time: "",
    },
    county: {
      name: "",
      category: "",
      code: "",
      declaring_region_or_unit: "",
      inclusion_time: "",
    },
  },
  involved_ethnic_groups: "",
  geographical_location_and_distribution_area: "",
  project_introduction: "",
  inheritance_context: "",
  social_function_and_cultural_significance: "",
  protection_unit_info: {
    unit_name: "",
    legal_person_type: "",
  },
  protection_measures_and_effectiveness: "",
  funding_for_protection: {
    total_amount: 0,
    change_in_funding: "maintained",
    funding_sources: ["government_input"],
  },
  practice_situation: {
    has_fixed_practice_venue: false,
    practice_frequency_change: "maintained",
    basic_practice_method_maintained: false,
    has_new_creation_or_development: false,
    audience_size_change: "maintained",
    related_income_change: "maintained",
  },
  inheritance_status: {
    representative_inheritors_at_all_levels: {
      national: 0,
      provincial: 0,
      municipal: 0,
      county: 0,
    },
    change_in_number_of_apprentices_of_representative_inheritors: "maintained",
    change_in_size_of_inheritance_group: "maintained",
    age_structure_of_inheritance_group: "reasonable_mix",
    has_venue_for_inheritance_activities: false,
    inheritance_group_capacity_building: {
      has_training_activities: false,
      training_activity_types: [],
    },
  },
  promotion_and_display_status: {
    has_promotion_and_display_activities: false,
    activity_scales: [],
    frequency_change_of_activities: "maintained",
    media_attention: "low",
    into_school_situation: {
      included_in_curriculum: false,
      included_in_textbooks: false,
      has_campus_activities: false,
    },
  },
  status_of_related_artifacts_and_sites: false,
  main_threats_to_inheritance_and_development: "",
  overall_survival_status: "good",
  future_protection_plan: "",
};

// 监听外部 visible，弹窗打开时初始化 localDetails 并补全字段
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      const rawData = cloneDeep(props.details || {});
      if (rawData.details && typeof rawData.details === "object") {
        // 把 details 字段内容合并到根对象，方便模板访问
        Object.assign(rawData, rawData.details);
        delete rawData.details; // 可选，避免重复
      }
      localDetails.value = rawData;
      initEmptyObject(localDetails.value, emptyDetailsTemplate);
    }
  }
);

// 同步内部 dialogVisible 变化给父组件
watch(dialogVisible, (val) => {
  emit("update:visible", val);
});

function handleSave() {
  console.log("✅ 提交前的 localDetails:", localDetails.value);
  emit("save", cloneDeep(localDetails.value));
  dialogVisible.value = false;
}

function handleClose() {
  dialogVisible.value = false;
}
</script>

<style scoped>
.section {
  margin-bottom: 20px;
}
.uniform-descriptions .el-descriptions__body {
  line-height: 1.6;
}
.readonly-text {
  white-space: pre-wrap;
  min-height: 76px; /* 保证和 textarea rows=3 一致 */
  display: flex;
  align-items: center;
}
</style>
