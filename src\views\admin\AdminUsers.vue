<template>
  <div class="user-management">
    <h3>用户管理</h3>
    <el-button type="primary" style="margin: 10px 0" @click="openAddDialog"
      >新增用户</el-button
    >
    <el-button
      type="danger"
      style="margin: 10px"
      :disabled="!multipleSelection.length"
      @click="handleBatchDelete"
    >
      批量删除
    </el-button>

    <!-- 筛选区 -->
    <el-form :inline="true" :model="queryParams" class="filter-form">
      <el-form-item label="用户名">
        <el-input
          v-model="queryParams.username"
          placeholder="输入用户名模糊查询"
          clearable
        />
      </el-form-item>
      <el-form-item label="角色">
        <el-select
          v-model="queryParams.role"
          placeholder="请选择角色"
          clearable
        >
          <el-option label="管理员" value="admin" />
          <el-option label="普通用户" value="user" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadData">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 用户表格 -->
    <el-table
      :data="userList"
      style="width: 95%"
      border
      table-layout="auto"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <!-- 其他列保持不变 -->

      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="phone" label="手机号" />
      <el-table-column prop="role" label="角色">
        <template #default="{ row }">
          <el-tag :type="row.role === 'admin' ? 'danger' : 'info'">
            {{ row.role === "admin" ? "系统管理员" : "普通用户" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'warning'">
            {{ row.status === 1 ? "启用" : "禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="avatar" label="头像">
        <template #default="{ row }">
          <el-image
            v-if="row.avatar"
            :src="row.avatar"
            :preview-src-list="[row.avatar]"
            fit="contain"
            preview-teleported
            style="
              width: 32px;
              height: 32px;
              border-radius: 50%;
              cursor: pointer;
            "
          />
          <span v-else>无</span>
        </template>
      </el-table-column>

      <el-table-column prop="created_time" label="创建时间">
        <template #default="{ row }">
          {{ dayjs(row.created_time).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
      </el-table-column>
      <el-table-column prop="updated_time" label="更新时间">
        <template #default="{ row }">
          {{ dayjs(row.updated_time).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="openEditDialog(row)"
              >编辑</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row.id)"
              >删除</el-button
            >
            <el-button type="warning" size="small" @click="toggleStatus(row)">
              {{ row.status === 1 ? "禁用" : "启用" }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      layout="total, prev, pager, next, jumper"
      @current-change="loadData"
      @size-change="loadData"
      class="mt-4"
    />

    <!-- 编辑用户弹窗 -->
    <el-dialog v-model="editDialogVisible" title="编辑用户" width="500px">
      <el-form
        :model="editForm"
        :rules="userRules"
        ref="editFormRef"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" disabled />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="editForm.role" placeholder="选择角色">
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="editForm.status"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="头像">
          <el-upload
            class="avatar-uploader"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :on-success="handleEditAvatarSuccess"
            action="/api/admin/upload"
          >
            <img v-if="editForm.avatar" :src="editForm.avatar" class="avatar" />
            <el-icon v-else><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEdit">保存</el-button>
      </template>
    </el-dialog>

    <!-- 新增用户弹窗 -->
    <el-dialog v-model="addDialogVisible" title="新增用户" width="500px">
      <el-form
        :model="addForm"
        :rules="userRules"
        ref="addFormRef"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="addForm.username" />
        </el-form-item>
        <el-form-item label="密码" prop="pwd">
          <el-input v-model="addForm.pwd" type="password" show-password />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="addForm.phone" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="addForm.role" placeholder="选择角色">
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="addForm.status"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="头像">
          <el-upload
            class="avatar-uploader"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :on-success="handleAvatarSuccess"
            action="/api/admin/upload"
          >
            <img v-if="addForm.avatar" :src="addForm.avatar" class="avatar" />
            <el-icon v-else><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAdd">保存</el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="avatarDialogVisible"
      width="auto"
      title="头像预览"
      :close-on-click-modal="true"
      align-center
    >
      <img
        :src="previewAvatarUrl"
        style="
          max-width: 100%;
          max-height: 70vh;
          display: block;
          margin: 0 auto;
        "
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getUserList, addUser, updateUser, deleteUser } from "@/api/admin";
import dayjs from "dayjs";
import { Plus } from "@element-plus/icons-vue";
const userRules = reactive({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 1, max: 20, message: "用户名长度需在 3~20 个字符", trigger: "blur" },
  ],
  pwd: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码至少 6 位", trigger: "blur" },
  ],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "手机号格式不正确", trigger: "blur" },
  ],
  role: [{ required: true, message: "请选择角色", trigger: "change" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
});
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  username: "",
  role: "",
  status: null,
});

const roleOptions = [
  { label: "系统管理员", value: "admin" },
  { label: "普通用户", value: "user" },
];

const userList = ref([]);
const total = ref(0);

const loadData = async (goToFirstPage = false) => {
  if (goToFirstPage) queryParams.value.pageNum = 1;
  const res = await getUserList(queryParams.value);
  if (res.code === 200) {
    userList.value = res.data.records;
    total.value = res.data.total;
  }
};

const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    username: "",
    role: "",
    status: null,
  };
  loadData(true);
};

// 编辑用户
const editDialogVisible = ref(false);
const editForm = ref({});
const editFormRef = ref();
const openEditDialog = (row) => {
  editForm.value = { ...row };
  editDialogVisible.value = true;
};
const handleEditAvatarSuccess = (res) => {
  editForm.value.avatar = res.data;
};

const submitEdit = () => {
  editFormRef.value.validate(async (valid) => {
    if (valid) {
      const res = await updateUser(editForm.value);
      if (res.code === 200) {
        ElMessage.success("更新成功");
        editDialogVisible.value = false;
        loadData();
      }
    }
  });
};

// 删除用户
const handleDelete = async (id) => {
  await ElMessageBox.confirm("确定删除该用户吗？", "提示", { type: "warning" });
  const res = await deleteUser(id);
  if (res.code === 200) {
    ElMessage.success("删除成功");
    loadData();
  }
};

// 状态切换
const toggleStatus = async (row) => {
  const res = await updateUser({ ...row, status: row.status === 1 ? 0 : 1 });
  if (res.code === 200) {
    ElMessage.success("状态已更新");
    loadData();
  }
};

// 新增用户
const addDialogVisible = ref(false);
const addForm = ref({});
const addFormRef = ref();
const openAddDialog = () => {
  addForm.value = {
    username: "",
    pwd: "",
    phone: "",
    avatar: "",
    role: "user",
    status: 1,
  };
  addDialogVisible.value = true;
};
const submitAdd = () => {
  addFormRef.value.validate(async (valid) => {
    if (valid) {
      const res = await addUser(addForm.value);
      if (res.code === 200) {
        ElMessage.success("新增成功");
        addDialogVisible.value = false;
        loadData(true);
      }
    }
  });
};
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith("image/");
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isImage) ElMessage.error("请上传图片文件");
  if (!isLt2M) ElMessage.error("图片大小不能超过2MB");
  return isImage && isLt2M;
};
const handleAvatarSuccess = (res) => {
  addForm.value.avatar = res.data;
};
const multipleSelection = ref([]);

const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};

const handleBatchDelete = async () => {
  if (multipleSelection.value.length === 0) return;
  await ElMessageBox.confirm(
    `确定删除这 ${multipleSelection.value.length} 个用户吗？`,
    "提示",
    { type: "warning" }
  );
  const ids = multipleSelection.value.map((item) => item.id);
  const res = await deleteUser(ids); // 你的 deleteUser 方法已支持批量删除
  if (res.code === 200) {
    ElMessage.success("删除成功");
    loadData();
    multipleSelection.value = [];
  }
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.filter-form {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.filter-form .el-form-item {
  min-width: 220px;
}

.mt-4 {
  margin-top: 20px;
}
.avatar-uploader {
  display: flex;
  align-items: center;
}
.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}
</style>
