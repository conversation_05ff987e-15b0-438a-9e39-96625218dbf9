import request from '@/request';

/**
 * 获取非遗项目详情（包括基本信息、资源、详情字段）
 * @param {number|string} id - 非遗项目 ID
 * @returns {Promise<Object>} 接口返回的数据
 */
export function fetchHeritageProjectDetail(id) {
  return request({
    url: `/heritageProjectEntity/detail/${id}`,
    method: 'get',
  });
}

export function updateHeritageProject(data) {
    return request({
      url: '/heritageProjectEntity/update',
      method: 'put',
      data
    });
  }
  
export function createHeritageProject(data) {
    return request({
      url: '/heritageProjectEntity/add',
      method: 'post',
      data
    });
  }
  export function deleteHeritageProject(id) {
    return request({
      url: `/heritageProjectEntity/delete/${id}`,
      method: 'delete',
    });
  }
  /**
 * 批量删除非遗项目
 * @param {Array<number|string>} ids - 要删除的项目ID数组
 * @returns {Promise<Object>} 接口返回的数据
 */
export function deleteHeritageProjectsBatch(ids) {
  return request({
    url: '/heritageProjectEntity/batchDelete',
    method: 'post', // 可改为 delete + body，根据后端实现
    data: ids
  });
}
