import axios from "axios";
import { baseURL, timeout, headers } from "@/config";
import { useTokenStore } from "@/stores/useTokenStore.js"; // 修正路径
import router from "@/router"; // 确保导入你的路由实例

// 创建 axios 实例
const request = axios.create({
  baseURL,
  timeout,
  headers,
});

// 添加请求拦截器
// 添加请求拦截器
request.interceptors.request.use(
  config => {
    const token = useTokenStore().token
    // 任何以这些前缀开头的接口都必须带 token
    const protectedPrefixes = ['/admin', '/userEntity']

    const needAuth = config.url?.startsWith('/admin') ||
    (config.url?.startsWith('/userEntity') &&
     !config.url.endsWith('/login') &&
     !config.url.endsWith('/register'))


    if (needAuth) {
      if (!token) {
        router.push('/login')
        return Promise.reject(new Error('请先登录'))
      }
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  error => Promise.reject(error)
)


// 添加响应拦截器
request.interceptors.response.use(
  function (response) {
    // 2xx 范围内的状态码都会触发该函数。
    // 对响应数据做点什么
    return response.data;
  },
  function (error) {
    // 超出 2xx 范围的状态码都会触发该函数。
    // 对响应错误做点什么

    // 如果响应状态为 401，则表示 token 失效或未登录
    if (error.response && error.response.status === 401) {
      localStorage.removeItem("token"); // 清除过期的 token
      router.push("/login"); // 重定向到登录页面
    }
    return Promise.reject(error);
  }
);

export default request;
