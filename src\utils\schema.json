{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"project_name": {"type": "string"}, "inclusion_in_lists": {"type": "object", "properties": {"national": {"type": "object", "properties": {"name": {"type": "string"}, "category": {"type": "string"}, "code": {"type": "string"}, "declaring_region_or_unit": {"type": "string"}, "inclusion_time": {"type": "string"}}, "required": ["name", "category", "code", "declaring_region_or_unit", "inclusion_time"]}, "provincial": {"type": "object", "properties": {"name": {"type": "string"}, "category": {"type": "string"}, "code": {"type": "string"}, "declaring_region_or_unit": {"type": "string"}, "inclusion_time": {"type": "string"}}, "required": ["name", "category", "code", "declaring_region_or_unit", "inclusion_time"]}, "municipal": {"type": "object", "properties": {"name": {"type": "string"}, "category": {"type": "string"}, "code": {"type": "string"}, "declaring_region_or_unit": {"type": "string"}, "inclusion_time": {"type": "string"}}, "required": ["name", "category", "code", "declaring_region_or_unit", "inclusion_time"]}, "county": {"type": "object", "properties": {"name": {"type": "string"}, "category": {"type": "string"}, "code": {"type": "string"}, "declaring_region_or_unit": {"type": "string"}, "inclusion_time": {"type": "string"}}, "required": ["name", "category", "code", "declaring_region_or_unit", "inclusion_time"]}}, "required": ["national", "provincial", "municipal", "county"]}, "involved_ethnic_groups": {"type": "string"}, "geographical_location_and_distribution_area": {"type": "string"}, "project_introduction": {"type": "string"}, "inheritance_context": {"type": "string"}, "social_function_and_cultural_significance": {"type": "string"}, "protection_unit_info": {"type": "object", "properties": {"unit_name": {"type": "string"}, "legal_person_type": {"type": "string"}}, "required": ["unit_name", "legal_person_type"]}, "protection_measures_and_effectiveness": {"type": "string"}, "funding_for_protection": {"type": "object", "properties": {"total_amount": {"type": "number"}, "change_in_funding": {"type": "string", "enum": ["maintained", "increased", "decreased"]}, "funding_sources": {"type": "array", "items": {"type": "string", "enum": ["government_input", "market_revenue", "social_donations", "others"]}}}, "required": ["total_amount", "change_in_funding", "funding_sources"]}, "practice_situation": {"type": "object", "properties": {"has_fixed_practice_venue": {"type": "boolean"}, "practice_frequency_change": {"type": "string", "enum": ["maintained", "expanded", "shrunk"]}, "basic_practice_method_maintained": {"type": "boolean"}, "has_new_creation_or_development": {"type": "boolean"}, "audience_size_change": {"type": "string", "enum": ["maintained", "expanded", "shrunk"]}, "related_income_change": {"type": "string", "enum": ["maintained", "increased", "decreased"]}}, "required": ["has_fixed_practice_venue", "practice_frequency_change", "basic_practice_method_maintained", "has_new_creation_or_development", "audience_size_change", "related_income_change"]}, "inheritance_status": {"type": "object", "properties": {"representative_inheritors_at_all_levels": {"type": "object", "properties": {"national": {"type": "integer"}, "provincial": {"type": "integer"}, "municipal": {"type": "integer"}, "county": {"type": "integer"}}, "required": ["national", "provincial", "municipal", "county"]}, "change_in_number_of_apprentices_of_representative_inheritors": {"type": "string", "enum": ["maintained", "increased", "decreased"]}, "change_in_size_of_inheritance_group": {"type": "string", "enum": ["maintained", "expanded", "shrunk"]}, "age_structure_of_inheritance_group": {"type": "string", "enum": ["reasonable_mix", "mostly_middle_aged_and_elderly", "serious_aging"]}, "has_venue_for_inheritance_activities": {"type": "boolean"}, "inheritance_group_capacity_building": {"type": "object", "properties": {"has_training_activities": {"type": "boolean"}, "training_activity_types": {"type": "array", "items": {"type": "string"}}}, "required": ["has_training_activities", "training_activity_types"]}}, "required": ["representative_inheritors_at_all_levels", "change_in_number_of_apprentices_of_representative_inheritors", "change_in_size_of_inheritance_group", "age_structure_of_inheritance_group", "has_venue_for_inheritance_activities", "inheritance_group_capacity_building"]}, "promotion_and_display_status": {"type": "object", "properties": {"has_promotion_and_display_activities": {"type": "boolean"}, "activity_scales": {"type": "array", "items": {"type": "string"}}, "frequency_change_of_activities": {"type": "string", "enum": ["maintained", "expanded", "shrunk"]}, "media_attention": {"type": "string", "enum": ["low", "medium", "high"]}, "into_school_situation": {"type": "object", "properties": {"included_in_curriculum": {"type": "boolean"}, "included_in_textbooks": {"type": "boolean"}, "has_campus_activities": {"type": "boolean"}}, "required": ["included_in_curriculum", "included_in_textbooks", "has_campus_activities"]}}, "required": ["has_promotion_and_display_activities", "activity_scales", "frequency_change_of_activities", "media_attention", "into_school_situation"]}, "status_of_related_artifacts_and_sites": {"type": "boolean"}, "main_threats_to_inheritance_and_development": {"type": "string"}, "overall_survival_status": {"type": "string", "enum": ["good", "fair", "average", "urgent_protection_needed"]}, "future_protection_plan": {"type": "string"}}, "required": ["project_name", "inclusion_in_lists", "involved_ethnic_groups", "geographical_location_and_distribution_area", "project_introduction", "inheritance_context", "social_function_and_cultural_significance", "protection_unit_info", "practice_situation", "inheritance_status", "promotion_and_display_status", "status_of_related_artifacts_and_sites", "main_threats_to_inheritance_and_development", "overall_survival_status", "protection_measures_and_effectiveness", "funding_for_protection", "future_protection_plan"]}