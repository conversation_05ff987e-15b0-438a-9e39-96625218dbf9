import request from "@/request";
import { ElMessage } from 'element-plus';

export function login(credentials) {
  return request.post("/userEntity/login", credentials)
    .then((response) => {
      console.log("🚀 login response", response);

      // 检查响应是否成功
      if (response && response.success) {
        // 如果有token，保存到localStorage
        if (response.data && response.data.token) {
          localStorage.setItem("token", response.data.token);
        }
        ElMessage.success(response.message || "登录成功");
      } else {
        ElMessage.error(response?.message || "登录失败");
      }

      return response;
    })
    .catch((error) => {
      console.error("� login error", error);

      // 处理不同类型的错误
      if (error.response) {
        // 服务器返回了错误状态码
        const status = error.response.status;
        const message = error.response.data?.message || error.message;

        switch (status) {
          case 502:
            ElMessage.error("服务器暂时无法访问，请稍后重试");
            break;
          case 500:
            ElMessage.error("服务器内部错误，请联系管理员");
            break;
          case 401:
            ElMessage.error("用户名或密码错误");
            break;
          case 403:
            ElMessage.error("访问被拒绝");
            break;
          default:
            ElMessage.error(message || `请求失败 (${status})`);
        }
      } else if (error.request) {
        // 请求已发出但没有收到响应
        ElMessage.error("网络连接失败，请检查网络连接");
      } else {
        // 其他错误
        ElMessage.error(error.message || "登录请求失败");
      }

      // 重新抛出错误，让调用方可以处理
      throw error;
    });
}

// 注册功能
export function register(data) {
  return request.post("/userEntity/register", data)
    .then((response) => {
      console.log("🚀 register response", response);

      if (response && response.success) {
        ElMessage.success(response.message || "注册成功");
      } else {
        ElMessage.error(response?.message || "注册失败");
      }

      return response;
    })
    .catch((error) => {
      console.error("🚨 register error", error);

      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.message || error.message;

        switch (status) {
          case 502:
            ElMessage.error("服务器暂时无法访问，请稍后重试");
            break;
          case 409:
            ElMessage.error("用户已存在");
            break;
          case 400:
            ElMessage.error(message || "注册信息有误");
            break;
          default:
            ElMessage.error(message || `注册失败 (${status})`);
        }
      } else if (error.request) {
        ElMessage.error("网络连接失败，请检查网络连接");
      } else {
        ElMessage.error(error.message || "注册请求失败");
      }

      throw error;
    });
}
