<template>
  <!-- 顶部栏整体 -->
  <div class="top-nav">
    <!-- 页面标题（两侧小装饰图标） -->
    <div class="title-line">
      <i class="iconfont icon-tesexiaozhen"></i>
      <span class="title-text">{{ currentLabel }}</span>
      <i class="iconfont icon-tesexiaozhen"></i>
    </div>

    <!-- 装饰下划线 -->
    <div class="underline iconfont icon--bright reflect-right"></div>

    <!-- 横向菜单 -->
    <div class="menu-bar">
      <span
        v-for="item in menu"
        :key="item.path"
        :class="[
          'menu-item',
          { active: item.path === router.currentRoute.value.fullPath },
        ]"
        @click="handleSelect(item.path)"
      >
        {{ item.label }}
      </span>
    </div>

    <!-- 面包屑 -->
    <!-- 修改面包屑模板，循环渲染 breadcrumb -->
    <div class="breadcrumb">
      <span class="crumb-prefix">当前位置：</span>
      <template v-for="(item, i) in breadcrumb" :key="i">
        <span
          :class="['crumb', { clickable: item.path }]"
          @click="jump(item.path)"
          >{{ item.label }}</span
        >
        <span v-if="i < breadcrumb.length - 1" class="crumb-sep"></span>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, type ComputedRef } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useIndexStore } from "@/stores/index"; // 记得改成自己的文件名
/* ---------- 基础数据 ---------- */
const menu = [
  { label: "首页", path: "/home" },
  { label: "地图", path: "/map" },
  { label: "资源", path: "/resources" },
  { label: "新闻", path: "/article" },
  { label: "数字文化产品", path: "http://*************" },
  { label: "文化资源管理", path: "/admin" },
];

/* ---------- 路由、状态 ---------- */
const route = useRoute();
const router = useRouter();
const store = useIndexStore();

/* ---------- breadcrumb 类型 ---------- */
type Crumb = { label: string; path?: string };

/* ---------- 生成面包屑 ---------- */
const breadcrumb: ComputedRef<Crumb[]> = computed(() => {
  const list: Crumb[] = [{ label: "首页", path: "/home" }];

  if (route.path.startsWith("/article")) {
    list.push({ label: "新闻", path: "/article" });
    if (route.name === "新闻详情") list.push({ label: "新闻详情" });
    return list;
  }

  if (route.name === "HeritageProjectDetail") {
    list.push({ label: "资源", path: "/resources" });
    list.push({ label: "项目详情" }); // 无需 path ⇒ 不可点击
    return list;
  }

  const match = menu.find((m) => m.path === route.path);
  if (match) list.push({ label: match.label });
  return list;
});

/* ---------- 顶部标题（当前页名）---------- */
const currentLabel = computed(() => breadcrumb.value.at(-1)?.label ?? "");

/* ---------- 交互 ---------- */
function jump(path?: string) {
  if (path && path !== route.path) {
    router.push(path);
    store.changeSelectedKeys(path); // 已在 Pinia actions 中声明
  }
}

function handleSelect(path: string) {
  if (path !== route.path) {
    // 如果是管理员页面，在新窗口打开
    if (path === "/admin") {
      window.open(path, "_blank");
    }
    // 如果是数字文化产品链接，在新窗口打开外部链接
    else if (path === "http://*************") {
      window.open(path, "_blank");
    } else {
      router.push(path);
      store.changeSelectedKeys(path);
    }
  }
}
function toHome() {
  handleSelect("/home");
}
</script>

<style scoped>
.top-nav {
  margin-top: 41px;
  background: #fff;
}

/* 标题行 */
.title-line {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  font-size: 20px;
}
.title-text {
  font-weight: 600;
  padding: 0 8px;
}

/* 装饰下划线 */
.underline {
  width: 100%;
  height: 12px;
  margin: 8px auto;
  border-bottom: 2px solid #20b2aa;
}

.breadcrumb {
  display: flex; /* 从 inline-flex 改为 flex → 块级盒子 */
  justify-content: center; /* 让内容在卡片内部也居中 */
  width: fit-content; /* 卡片宽度随内容 */
  background: #ffffff;
  padding: 6px 16px;
  margin: 8px auto 0; /* 上 8px，左右 auto → 水平居中 */
  font-size: 14px;
  color: #666;
  gap: 6px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

/* “当前位置”灰度弱化 */
.crumb-prefix {
  color: #999;
  font-weight: 500;
}

/* 可点节点——深色+悬停下划线 */
.crumb {
  color: #333;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.2s;
}
.crumb:hover {
  color: #c00;
  text-decoration: underline;
}

/* 分隔符改成矢量箭头，字号稍小显轻盈 */
.crumb-sep {
  font-size: 12px;
  color: #bbb;
  pointer-events: none; /* 不抢点击 */
}
/* 用伪元素把 “>” 换成 ↗︎ 或 ▶︎ */
.crumb-sep::before {
  content: "▶";
  display: inline-block;
  transform: translateY(-1px) scale(0.8);
}
/* 点击态单独上色 */
.crumb.clickable {
  cursor: pointer;
  color: #333;
  font-weight: 600;
}
.crumb.clickable:hover {
  color: #c00;
  text-decoration: underline;
}

/* 横向菜单 */
.menu-bar {
  display: flex;
  justify-content: center;
  background: #20b2aa;
}
.menu-item {
  color: #fff;
  padding: 12px 28px;
  cursor: pointer;
  transition: background 0.2s;
}
.menu-item:hover {
  background: #1a998e;
}
.menu-item.active {
  background: #1a998e;
  border-bottom: 3px solid #b8860b;
}
.crumb-prefix {
  color: #666;
  margin-right: 4px;
}
</style>
