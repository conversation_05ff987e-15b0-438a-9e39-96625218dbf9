import request from "@/request";

// 查询非物质文化遗产项目列表（分页查询）
export function listHeritageprojects(query = { pageNum: 1, pageSize: 10 }) {
  return request({
    url: '/admin/heritageprojects/page',
    method: 'post',  // 更改为 POST 方法
    data: query
  });
}

// 查询非物质文化遗产项目详细
export function getHeritageprojects(id) {
  return request({
    url: `/admin/heritageprojects/detail/${id}`,
    method: 'get'
  });
}

// 新增非物质文化遗产项目
export function addHeritageprojects(data) {
  return request({
    url: '/admin/heritageprojects',
    method: 'post',
    data: data
  });
}

// 修改非物质文化遗产项目
export function updateHeritageprojects(data) {
  return request({
    url: '/admin/heritageprojects',
    method: 'put',
    data: data
  });
}

// 删除非物质文化遗产项目（支持单个和批量删除）
export function delHeritageprojects(ids) {
  return request({
    url: `/admin/heritageprojects/${Array.isArray(ids) ? ids.join(',') : ids}`,
    method: 'delete'
  });
}
export function getUserList(query) {
  return request({
    url: '/userEntity/page',
    method: 'post',
    data: query
  })
}
// 新增用户 ✅
export function addUser(data) {
  return request({
    url: '/userEntity',
    method: 'post',
    data
  })
}

// 更新用户 ✅
export function updateUser(data) {
  return request({
    url: '/userEntity',
    method: 'put',
    data
  })
}

// 删除用户 ✅
export function deleteUser(ids) {
  return request({
    url: `/userEntity/${Array.isArray(ids) ? ids.join(',') : ids}`,
    method: 'delete'
  })
}
// 获取当前用户信息
export function getCurrentUserInfo() {
  return request({
    url: '/userEntity/info',
    method: 'get',
    headers: {
      Authorization: 'Bearer ' + localStorage.getItem('token')
    }
  })
}
/**
 * 当前用户修改自己的信息
 * @param {*} data 包含 phone、password、oldPassword、avatar 等字段
 * @returns 
 */
export const updateSelfInfo = (data) => {
  return request.post("/userEntity/selfUpdate", data);
}
//  获取管理员统计信息 ✅
;
export function fetchAdminStats() {
  return request({
    url: '/admin/stats',
    method: 'get'
  });
}