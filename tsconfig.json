{
  "compilerOptions": {
    /* ---------- 基础编译选项 ---------- */
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "node",
    "lib": ["ESNext", "DOM"],

    /* ---------- 路径映射 ---------- */
    "baseUrl": ".",
    "paths": { "@/*": ["src/*"] },

    /* ---------- 兼容性 / 体验 ---------- */
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,

    /* ---------- 类型检查 ---------- */
    "strict": true,
    "skipLibCheck": true,               // 忽略 node_modules 的声明冲突
    "types": ["vite/client"],           // 让 import.meta.env 有类型

    /* ---------- 不生成 JS ---------- */
    "noEmit": true                      // 防止 tsc 把源码编译成 .js

    /* ---------- 其他 ---------- */
    // "jsx": "preserve",               // 如果需要 TSX/JSX 保留
    // "sourceMap": true               // 通常 Dev 环境用不到
  },

  "include": ["env.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"],
  "exclude": ["src/**/__tests__/**"]
}
