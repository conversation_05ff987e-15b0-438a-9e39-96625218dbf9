<template>
  <div class="main">
    <!-- 顶部切页按钮 -->
    <div class="padding-lr-xxsl">
      <el-row :gutter="200">
        <el-col v-for="n in 4" :span="6" :key="n">
          <div
            class="w-100 h-50 line-h-50 text-center cursor text-overflow-sub bg-red-hover"
            :class="activeIndex === n ? 'bg-red' : 'bg-white'"
            @click="activeIndex = n"
          >
            {{ ["基本信息", "我的预约", "我的收藏", "我的消息"][n - 1] }}
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 内容区 -->
    <div class="margin-top-lg padding-bottom-xl">
      <!-- 基本信息 -->
      <div v-if="activeIndex === 1" class="bg-white margin-top-xs">
        <el-row class="h-300px bg-white padding-lg margin-top-xl">
          <el-col :span="5">
            <div class="w-fill h-fill border" />
          </el-col>
          <el-col :span="19">
            <div class="w-fill h-fill padding-lr-xl">
              <div class="line-h-30 margin-bottom text-black">
                <div class="text-red-hover cursor">账号：{{ me.acc }}</div>
                <div class="text-red-hover cursor">密码：{{ me.pwd }}</div>
                <div class="text-red-hover cursor">
                  真实姓名：{{ me.realname }}
                </div>
                <div class="text-red-hover cursor">
                  收件地址：{{ me.address }}
                </div>
                <div class="text-red-hover cursor">
                  邮政编码：{{ me.postalcode }}
                </div>
              </div>
              <div class="flex align-center justify-start text-xxs">
                <div v-if="!me.acc" class="btn" @click="$router.push('/login')">
                  前往登录
                </div>
                <div class="btn">修改信息</div>
                <div class="btn" @click="authentication">实名认证</div>
                <div v-if="me.acc" class="btn" @click="logout">退出登录</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 我的预约 -->
      <div
        class="w-100 text-xxs bg-white"
        v-for="item in appointment"
        v-if="activeIndex === 2"
      >
        <el-row
          :gutter="0"
          class="h-300px bg-white padding-tb-lg margin-top-xl"
        >
          <el-col :span="9">
            <div
              class="w-fill h-fill bg-red margin-lr-xl"
              style="
                background: url(https://www.ihchina.cn/Uploads/Picture/2023/11/26/s6562ee94387ed.jpg)
                  no-repeat center/100% 100%;
              "
            ></div>
          </el-col>
          <el-col :span="15">
            <div class="w-fill h-fill bg-white padding-lr-xl">
              <div class="text-red-hover cursor">
                <div class="padding-top-xs">
                  林岩：“两创”视域下北京雕漆创新设计的文化生态探究
                </div>
                <div
                  class="margin-tb-xs padding-tb-xs text-xxs border-bottom-da"
                >
                  2023.11.26
                </div>
              </div>
              <div class="text-xxs line-h-20 text-overflow-treble">
                随着非遗保护工作的深度开展，北京雕漆技艺当前取得了一些成果，尤其是在“两创”视域下，生态融合的格局得以凸显，呈现出差异化与多元化的生态，但在保留原样还是创新发展上仍存有诸多争议。基于实践考察发现，北京雕漆技艺创新发展存在局限与问题。从创新设计的视角对其进行重新...
              </div>
              <div
                class="fl w-fill flex align-center justify-start margin-top text-xxs text-red-hover"
              >
                <div
                  class="iconfont icon-gufengwujianzhongguofengzhongguojie_huaban_huaban cursor"
                ></div>
                <div class="text-center ff text-bold padding-lr-xs cursor">
                  查看更多
                </div>
                <div
                  class="iconfont icon-gufengwujianzhongguofengzhongguojie_huaban_huaban cursor"
                ></div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 我的收藏 -->
      <template v-if="activeIndex === 3">
        <div
          v-for="c in collection"
          :key="c.id"
          class="w-100 h-60px bg-white margin-top-xs flex align-center justify-between padding-lr-xl"
        >
          <div>{{ c.label }}</div>
          <div>{{ c.date }}</div>
        </div>
        <div v-if="!collection.length" class="text-center text-black">
          暂无数据
        </div>
      </template>

      <!-- 我的消息 -->
      <template v-if="activeIndex === 4">
        <div
          v-for="n in news"
          :key="n.id"
          class="w-100 h-60px bg-white margin-top-xs flex align-center justify-between padding-lr-xl text-red-hover cursor"
        >
          <div>{{ n.label }}</div>
          <div>{{ n.date }}</div>
        </div>
        <div v-if="!news.length" class="text-center text-black">暂无数据</div>
      </template>
    </div>

    <common-notify
      v-if="notifyShow"
      :msg="notifyMsg"
      :notify-type="notifyType"
      :close-notify="closeNotify"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import router from "@/router";
import CommonNotify from "@/components/CommonNotify.vue";

interface User {
  acc: string;
  pwd: string;
  realname: string;
  address: string;
  postalcode: string;
}

interface Item {
  id: number;
  label: string;
  date: string;
}

const activeIndex = ref(1);

const me = computed<User>(() => {
  const raw = localStorage.getItem("myself");
  return raw
    ? (JSON.parse(raw) as User)
    : { acc: "", pwd: "", realname: "", address: "", postalcode: "" };
});

const appointment = ref<Item[]>([]);
const collection = ref<Item[]>([]);
const news = ref<Item[]>([
  { id: 1, label: "中华人民共和国非物质文化遗产法…", date: "2011" },
]);

function logout(): void {
  localStorage.clear();
  router.push("/login");
}

const notifyMsg = ref("");
const notifyType = ref(true);
const notifyShow = ref(false);

function authentication(): void {
  notifyMsg.value = "该功能暂未开通";
  notifyType.value = false;
  notifyShow.value = true;
  setTimeout(() => (notifyShow.value = false), 1500);
}

function closeNotify(): void {
  notifyShow.value = false;
}
</script>

<style scoped>
.bg-red {
  background: darkred;
}
.bg-red-hover {
  transition: 0.2s;
}
.bg-red-hover:hover {
  background: darkred;
  color: #fff;
}
.text-red-hover:hover {
  color: darkred;
}
.btn {
  margin-right: 8px;
  background: #a89171;
  padding: 0 6px;
  cursor: pointer;
}
</style>
