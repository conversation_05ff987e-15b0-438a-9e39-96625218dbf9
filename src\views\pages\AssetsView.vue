<template>
  <div
    class="heritage-project-list"
    style="margin-top: 0px; background-color: #e6e6e6"
  >
    <!-- 筛选区域 -->
    <div class="filter-options">
      <select v-model="filters.projectLevel">
        <option value="">全部级别</option>
        <option value="国家级">国家级</option>
        <option value="自治区级">自治区级</option>
        <option value="市级">市级</option>
      </select>

      <select v-model="filters.category">
        <option value="">全部类别</option>
        <option
          v-for="category in categories"
          :key="category"
          :value="category"
        >
          {{ category }}
        </option>
      </select>

      <select v-model="filters.region">
        <option value="">全部地区</option>
        <option v-for="region in regions" :key="region" :value="region">
          {{ region }}
        </option>
      </select>

      <input
        v-model="filters.name"
        placeholder="请输入项目名称"
        style="padding: 10px; font-size: 16px"
      />
      <button class="blue-button" @click="searchProjects">搜索</button>
      <button @click="resetFilters" class="gray-button">重置</button>
    </div>

    <!-- 加载中 -->
    <div v-if="loading" style="text-align: center; margin: 30px">加载中...</div>

    <!-- 项目列表 -->
    <div v-else-if="projects.records.length > 0" class="project-grid">
      <router-link
        v-for="project in projects.records"
        :key="project.id"
        class="project-card"
        :to="{
          name: 'HeritageProjectDetail',
          params: { id: project.id },
          query: {
            page: currentPage,
            projectLevel: filters.projectLevel,
            category: filters.category,
            region: filters.region,
            name: filters.name,
          },
        }"
      >
        <div class="project-content">
          <div
            class="project-cover"
            :style="{ backgroundImage: `url(${project.src})` }"
          ></div>
        </div>
        <h3 class="project-title">{{ project.name }}</h3>
        <p class="project-description">
          {{
            project.content.length > 20
              ? project.content.substring(0, 20) + "..."
              : project.content
          }}
        </p>
      </router-link>
    </div>

    <!-- 无数据提示 -->
    <div v-else style="text-align: center; margin: 30px; color: gray">
      暂无符合条件的非遗项目
    </div>

    <!-- 分页控制 -->
    <div class="pagination">
      <button @click="changePage(1)" :disabled="currentPage === 1">首页</button>
      <button
        @click="changePage(currentPage - 1)"
        :disabled="currentPage === 1"
      >
        上一页
      </button>

      <button
        v-for="page in visiblePages"
        :key="page"
        @click="changePage(page)"
        :class="{ active: currentPage === page }"
      >
        {{ page }}
      </button>

      <button
        @click="changePage(currentPage + 1)"
        :disabled="currentPage === totalPages"
      >
        下一页
      </button>
      <button
        @click="changePage(totalPages)"
        :disabled="currentPage === totalPages"
      >
        末页
      </button>

      <!-- 跳页输入 -->
      <span style="margin-left: 10px">
        跳至
        <input
          v-model.number="jumpPage"
          type="number"
          min="1"
          :max="totalPages"
          class="jump-input"
          @keyup.enter="changePage(jumpPage)"
        />
        页
        <button @click="changePage(jumpPage)">跳转</button>
      </span>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { pageQuery } from "@/api/page";
import { useRoute } from "vue-router";
const route = useRoute();
const projects = reactive({ records: [] });
const totalPages = ref(1);
const currentPage = ref(1);
const loading = ref(false);

const filters = reactive({
  projectLevel: "",
  category: "",
  region: "",
  name: "",
});

const categories = [
  "民间文学",
  "传统音乐",
  "传统舞蹈",
  "传统戏剧",
  "曲艺",
  "体育、游艺与杂技",
  "传统美术",
  "传统技艺",
  "传统医药",
  "民俗",
];

const regions = [
  "东胜区",
  "康巴什区",
  "达拉特旗",
  "准格尔旗",
  "伊金霍洛旗",
  "鄂托克旗",
  "鄂托克前旗",
  "杭锦旗",
  "乌审旗",
];

// 项目搜索（搜索按钮点击）
const searchProjects = () => {
  currentPage.value = 1;
  fetchProjects();
};

// 加载数据
const fetchProjects = async () => {
  loading.value = true;
  const res = await pageQuery({
    pageNum: currentPage.value,
    pageSize: 8,
    projectLevel: filters.projectLevel,
    category: filters.category,
    region: filters.region,
    name: filters.name,
  });
  if (res.success && res.data) {
    projects.records = res.data.records;
    totalPages.value = Math.ceil(res.data.total / 8);
  } else {
    projects.records = [];
  }
  loading.value = false;
};

const changePage = (page) => {
  if (page > 0 && page <= totalPages.value) {
    currentPage.value = page;
    fetchProjects();
  }
};

const resetFilters = () => {
  filters.projectLevel = "";
  filters.category = "";
  filters.region = "";
  filters.name = "";
  currentPage.value = 1;
  fetchProjects();
};
const jumpPage = ref(null);

const visiblePages = computed(() => {
  const range = 2;
  const pages = [];
  const start = Math.max(1, currentPage.value - range);
  const end = Math.min(totalPages.value, currentPage.value + range);
  for (let i = start; i <= end; i++) pages.push(i);
  return pages;
});

onMounted(() => {
  filters.projectLevel = route.query.projectLevel || "";
  filters.category = route.query.category || "";
  filters.region = route.query.region || "";
  filters.name = route.query.name || "";
  currentPage.value = parseInt(route.query.page) || 1;
  fetchProjects();
});
</script>

<style scoped>
.heritage-project-list {
  padding: 20px 10px;
  text-align: center;
}

/* 筛选区域响应式布局 */
.filter-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}
.filter-options select,
.filter-options input,
.filter-options button {
  padding: 10px;
  font-size: 16px;
  min-width: 160px;
}

/* 响应式网格 */
.project-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  justify-items: center;
  margin-top: 20px;
}

/* 项目卡片 */
.project-card {
  width: 100%;
  max-width: 320px;
  height: 330px;
  padding: 10px;
  background-color: #e6e6e6;
  border-radius: 6px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  text-align: center;
}
.project-card:hover {
  transform: translateY(-4px);
}

/* 封面 */
.project-content {
  height: 180px;
  overflow: hidden;
  border-radius: 6px;
  position: relative;
}
.project-cover {
  width: 100%;
  height: 180px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 6px;
  transition: transform 0.4s ease;
  background-color: #f0f0f0;
}
.project-content:hover .project-cover {
  transform: scale(1.1);
}

/* 文本样式 */
.project-title {
  margin-top: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.project-description {
  font-size: 14px;
  color: black;
  margin-top: 8px;
  max-height: 1.4em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.3s ease;
  cursor: pointer;
}

/* 鼠标悬浮时展示完整内容 */
.project-description:hover {
  white-space: normal;
  max-height: 10em;
  overflow: visible;
}

/* 分页响应式居中 */
.pagination {
  margin-top: 30px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  align-items: center;
}

/* 平板：两列 */
@media (max-width: 1024px) {
  .project-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 手机：一列，筛选纵向 */
@media (max-width: 600px) {
  .project-grid {
    grid-template-columns: 1fr;
  }
  .filter-options {
    flex-direction: column;
    align-items: stretch;
  }
  .filter-options select,
  .filter-options input,
  .filter-options button {
    width: 100%;
  }
}
.blue-button {
  background-color: #007bff; /* 主流蓝色 */
  color: white;
  border: none;
  padding: 10px;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.blue-button:hover {
  background-color: #0056b3; /* 深一点的蓝色 */
}
.gray-button {
  background-color: #6c757d; /* 主流灰色 */
  color: white;
  border: none;
  padding: 10px;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.gray-button:hover {
  background-color: #5a6268; /* 深一点的灰色 */
}
.pagination button {
  background-color: white;
  border: 1px solid #ccc;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  transition: 0.2s ease-in-out;
}

.pagination button:hover:not(:disabled) {
  background-color: #f0f0f0;
}

.pagination button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.pagination .active {
  background-color: #007bff;
  color: white;
  font-weight: bold;
  border-color: #007bff;
}

.jump-input {
  width: 60px;
  padding: 6px;
  margin: 0 4px;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #ccc;
}
</style>
