// src/api/CountData.ts
import request from '@/request'

export interface ApiResp<T> {
  code: number
  success: boolean
  message: string
  data: T
}

export interface ProjectItem {
  region: string
  projectLevel: string
  category: string
}

// 分类数量
export function countByProjectlevel() {
  return request.get<ApiResp<{ level: string; count: number }[]>>(
    '/heritageProjectEntity/projectlevel'
  )
}

// 修改 getList 函数，返回 ApiResp 类型
export function getList (): Promise<ApiResp<ProjectItem[]>> {
  // 直接把拦截器抛出的那一层返回
  return request.get<ApiResp<ProjectItem[]>>('/heritageProjectEntity/list')
}