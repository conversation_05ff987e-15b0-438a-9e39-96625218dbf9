<template>
  <div style="background-color: #e6e6e6">
    <!-- 顶部区域：Logo 和右侧信息 -->
    <div
      class="flex flex-direction padding-lr-xl index-1 absolute-top bg-white text-black"
    >
      <el-row :gutter="80" class="padding-tb-xs border-bottom-da">
        <el-col :span="12">
          <!-- Logo + 站名 -->
          <div class="flex align-center logo-wrap">
            <img src="@/assets/ordos.jpg" alt="Logo" class="logo-img" />
            <span class="site-name">非物质文化遗产网</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="flex align-center justify-center">
            <div class="padding-lr-xs text-bold-1 margin-top-sm cursor">
              <i class="iconfont icon-search_sousuo"></i>
            </div>
            <div class="padding-lr-xs text-bold-1 margin-top-sm cursor">
              <i class="iconfont icon-session_huihua"></i>
            </div>
            <div class="padding-lr-xs text-bold-1 margin-top-sm cursor">
              <i class="iconfont icon-mine_wode"></i>
            </div>
            <div
              class="margin-top-sm margin-left-sm ff text-overflow-sub text-bold"
            >
              {{ new Date().getFullYear() }}-{{ new Date().getMonth() + 1 }}-{{
                new Date().getDate()
              }}
              {{ "星期" + "日一二三四五六".charAt(new Date().getDay()) }}
              农历{{ lunardate }}
            </div>
            <div
              class="padding-lr-xs text-bold-1 margin-top-sm cursor margin-left-sm"
            >
              <i class="iconfont icon-knot"></i>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from "vue";
import router from "@/router";
import { useIndexStore } from "@/stores";
import {
  User,
  House,
  Location,
  FolderOpened,
  ChatDotRound,
  Setting,
} from "@element-plus/icons-vue";
//@ts-ignore
import lunardate from "@/utils/solarlunar.js";

const menu = ref([
  { label: "首页", path: "/home" },
  { label: "地图", path: "/map" },
  { label: "资源", path: "/resources" },
  { label: "新闻", path: "/article" },
  { label: "管理员", path: "/admin" },
]);

// 图标映射：按 label 匹配图标组件
const icons = {
  首页: House,
  地图: Location,
  资源: FolderOpened,
  新闻: ChatDotRound,
  管理员: Setting,
};

const activeIndex = ref("/home");
const state = reactive({
  selectedKeys: computed(() => useIndexStore().selectedKeys),
});

onMounted(() => {
  activeIndex.value = router.currentRoute.value.fullPath;
  useIndexStore().changeSelectedKeys(activeIndex.value);
});

const handleSelect = (key: string) => {
  router.push(key);
  useIndexStore().changeSelectedKeys(key);
};

const mouse = ref("");
const transform = ref(0);

const mouseenter = (item: any, index: number) => {
  mouse.value = item.label;
  transform.value = 650 + 100 * (index + 1);
};

const mouseleave = (item: any, index: number) => {
  setTimeout(() => (mouse.value = ""), 20000);
};
</script>

<style scoped>
.nav-bg {
  background-color: #20b2aa;
}

.nav-item {
  display: flex;
  align-items: center;
  color: white;
  padding: 14px 20px;
  cursor: pointer;
  font-size: 20px; /* 字体变大 */
  transition: background-color 0.2s;
}
.nav-bar-full {
  width: 100%;
  height: 60px;
  padding: 0 60px; /* 左右边距 */
  box-sizing: border-box;
  justify-content: flex-start;
}
.nav-bar-full-fixed {
  width: 100vw; /* 强制宽度占满整个视口 */
  height: 60px;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  justify-content: flex-start;
}

.nav-item:hover {
  background-color: #1a998e;
}

.nav-item.active {
  border-bottom: 3px solid brown;
}

.nav-icon {
  margin-right: 6px;
}

.ml-auto {
  margin-left: auto;
}
/* 追加或覆盖原样式 */
.logo-wrap {
  display: flex;
  align-items: center;
  gap: 6px; /* 控制 logo 与文字的实际间隙，想更贴就改成 2px、4px */
}

.logo-img {
  height: 50px; /* 保持高度 */
  width: auto; /* 让宽度跟随内容 */
  max-width: 120px; /* 估算文字实际像素，再给一点余量 */
  object-fit: contain; /* 避免被拉伸 */
  object-position: left; /* 保证图像内容靠左显示 */
}

.site-name {
  font-size: 20px;
  font-weight: 600;
  white-space: nowrap; /* 防止文字换行，也避免被别的 util 类撑满整行 */
}
</style>
