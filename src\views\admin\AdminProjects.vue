<template>
  <div>
    <h3 style="margin-bottom: 16px">非遗项目管理</h3>
    <el-button type="primary" style="margin-bottom: 16px" @click="openDialog()"
      >新增项目</el-button
    >
    <el-button
      type="danger"
      style="margin-left: 8px; margin-bottom: 16px"
      @click="batchDelete"
      :disabled="!multipleSelection.length"
      >批量删除</el-button
    >
    <el-button
      type="warning"
      style="margin-left: 8px; margin-bottom: 16px"
      @click="batchEdit"
      :disabled="!multipleSelection.length"
      >批量修改</el-button
    >

    <!-- 筛选表单 -->
    <el-form :inline="true" :model="queryParams" class="filter-form">
      <el-form-item label="项目等级">
        <el-select
          v-model="queryParams.projectLevel"
          placeholder="全部"
          style="width: 140px"
        >
          <el-option label="全部" value="" />
          <el-option label="国家级" value="国家级" />
          <el-option label="自治区级" value="自治区级" />
          <el-option label="市级" value="市级" />
        </el-select>
      </el-form-item>

      <el-form-item label="项目类别">
        <el-select
          v-model="queryParams.category"
          placeholder="全部"
          style="width: 160px"
        >
          <el-option v-for="c in categoryList" :key="c" :label="c" :value="c" />
        </el-select>
      </el-form-item>

      <el-form-item label="地区">
        <el-select
          v-model="queryParams.region"
          placeholder="全部"
          style="width: 140px"
        >
          <el-option v-for="r in regionList" :key="r" :label="r" :value="r" />
        </el-select>
      </el-form-item>

      <el-form-item label="项目名称">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入关键词"
          clearable
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="fetchData">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格展示 -->
    <el-table
      :data="tableData"
      border
      style="width: 100%; font-size: 13px"
      :fit="true"
      :header-cell-style="{ background: '#f5f7fa', fontWeight: 'bold' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column type="index" label="#" width="50" />
      <el-table-column prop="name" label="项目名称" width="220">
        <template #default="{ row }">
          <el-tooltip :content="row.name" placement="top">
            <span>{{ row.name }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="projectLevel" label="项目等级" width="90" />
      <el-table-column prop="category" label="项目类别" width="100" />
      <el-table-column prop="region" label="地区" width="90" />
      <el-table-column prop="inheritor" label="代表性传承人" width="180">
        <template #default="{ row }">
          <el-tooltip :content="row.inheritor" placement="top">
            <span class="ellipsis-text">{{ row.inheritor }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="简介" width="180">
        <template #default="{ row }">
          <el-tooltip :content="row.content" placement="top">
            <span class="ellipsis-text">{{ row.content }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="src" label="文件地址" width="100">
        <template #default="{ row }">
          <a
            :href="row.src"
            target="_blank"
            rel="noopener noreferrer"
            style="color: #409eff; text-decoration: underline"
          >
            查看
          </a>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" width="160">
        <template #default="{ row }">
          <span>{{ formatTime(row.updatedTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="320">
        <template #default="scope">
          <el-button size="small" @click="edit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="remove(scope.row)"
            >删除</el-button
          >
          <el-button size="small" type="success" @click="viewDetail(scope.row)"
            >查看详情</el-button
          >
          <el-button
            size="small"
            type="warning"
            @click="viewResources(scope.row)"
            >查看资源</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 项目详情弹窗 -->
    <DetailDialog v-model:visible="detailDialogVisible" :details="detail" />
    <!-- 分页 -->
    <el-pagination
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      layout="total, prev, pager, next, sizes"
      :page-sizes="[10, 20, 30, 50]"
      @size-change="fetchData"
      @current-change="fetchData"
      style="margin-top: 20px; text-align: right"
    />

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑项目' : '新增项目'"
      width="600px"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="formData.name" />
        </el-form-item>
        <el-form-item label="项目等级" prop="projectLevel">
          <el-select v-model="formData.projectLevel" placeholder="请选择">
            <el-option label="国家级" value="国家级" />
            <el-option label="自治区级" value="自治区级" />
            <el-option label="市级" value="市级" />
          </el-select>
        </el-form-item>
        <el-form-item label="项目类别" prop="category">
          <el-select v-model="formData.category" placeholder="请选择">
            <el-option
              v-for="c in categoryList"
              :key="c"
              :label="c"
              :value="c"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="地区" prop="region">
          <el-select v-model="formData.region" placeholder="请选择">
            <el-option v-for="r in regionList" :key="r" :label="r" :value="r" />
          </el-select>
        </el-form-item>
        <el-form-item label="传承人">
          <el-input v-model="formData.inheritor" />
        </el-form-item>
        <el-form-item label="简介" prop="content">
          <el-input type="textarea" rows="3" v-model="formData.content" />
        </el-form-item>
        <el-form-item label="上传封面" prop="src">
          <div>
            <el-upload
              :action="uploadUrl"
              name="file"
              :before-upload="beforeUploadImageOnly"
              :show-file-list="false"
              :http-request="() => {}"
            >
              <el-button
                type="primary"
                :loading="isUploading && currentUploadFile?.type === 'cover'"
              >
                上传封面
              </el-button>
            </el-upload>

            <el-button
              v-if="formData.src"
              type="text"
              @click="viewUploadedFile"
              style="margin-left: 10px; color: #409eff"
            >
              查看
            </el-button>

            <el-button
              v-if="isUploading && currentUploadFile?.type === 'cover'"
              type="danger"
              size="small"
              @click="cancelUpload"
              style="margin-left: 10px"
            >
              取消上传
            </el-button>
          </div>

          <!-- 上传进度条 -->
          <el-progress
            v-if="isUploading && currentUploadFile?.type === 'cover'"
            :percentage="uploadProgress"
            :format="percentFormat"
            style="margin-top: 10px"
          />

          <!-- 预览封面图 -->
          <div
            v-if="formData.src"
            style="margin-top: 10px; display: flex; justify-content: center"
          >
            <img
              :src="formData.src"
              style="
                max-width: 100%;
                max-height: 300px;
                object-fit: contain;
                border: 1px solid #ddd;
                padding: 4px;
                background: #fff;
              "
            />
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="info" @click="openDetailDialog(formData)"
            >项目详情</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="openResourceDialog"
            >查看资源</el-button
          >
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </template>
    </el-dialog>
    <!-- 项目详情弹窗 -->
    <DetailDialog
      v-model:visible="detailDialogVisible"
      :details="detail"
      :editable="true"
      @save="handleDetailSave"
      @update:visible="(val) => (detailDialogVisible = val)"
    />
    <!-- 资源弹窗 -->
    <el-dialog
      v-model="resourceDialogVisible"
      :title="isResourceEditable ? '资源编辑' : '资源预览'"
      width="70%"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="isResourceEditable"
        :inline="true"
        label-width="60px"
        class="mb-4"
      >
        <el-form-item label="类型">
          <el-select v-model="newResource.type" style="width: 120px">
            <el-option label="图片" :value="1" />
            <el-option label="视频" :value="0" />
            <el-option label="音频" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="上传">
          <div>
            <el-upload
              :action="uploadUrl"
              name="file"
              :before-upload="beforeUploadWithTypeCheck"
              :show-file-list="false"
              :http-request="() => {}"
            >
              <el-button
                type="primary"
                :loading="isUploading && currentUploadFile?.type === 'resource'"
              >
                上传资源
              </el-button>
            </el-upload>

            <el-button
              v-if="isUploading && currentUploadFile?.type === 'resource'"
              type="danger"
              size="small"
              @click="cancelUpload"
              style="margin-left: 10px"
            >
              取消上传
            </el-button>
          </div>

          <!-- 上传进度条 -->
          <el-progress
            v-if="isUploading && currentUploadFile?.type === 'resource'"
            :percentage="uploadProgress"
            :format="percentFormat"
            style="margin-top: 10px"
          />

          <el-form-item label="预览">
            <div
              v-if="newResource.resourceUrl"
              style="cursor: pointer"
              @click="previewResource(newResource)"
            >
              <video
                v-if="newResource.type === 0"
                :src="newResource.resourceUrl"
                style="width: 120px; height: 80px; object-fit: cover"
                muted
                preload="metadata"
              />
              <audio
                v-else-if="newResource.type === 2"
                :src="newResource.resourceUrl"
                controls
                style="width: 120px; height: 40px"
                preload="metadata"
              />
              <img
                v-else
                :src="newResource.resourceUrl"
                style="width: 120px; height: 80px; object-fit: cover"
              />
            </div>
            <div
              v-else
              style="
                width: 120px;
                height: 80px;
                background: #f5f7fa;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #bbb;
              "
            >
              暂无资源
            </div>
          </el-form-item>
        </el-form-item>

        <el-form-item label="描述">
          <el-input v-model="newResource.description" style="width: 240px" />
        </el-form-item>

        <el-button type="success" @click="addResource">添加</el-button>
        <el-button type="danger" @click="resetNewResource">取消</el-button>
      </el-form>

      <el-table :data="tempResourceList" border style="width: 100%">
        <el-table-column label="预览" width="140">
          <template #default="{ row }">
            <div @click="previewResource(row)" style="cursor: pointer">
              <video
                v-if="row.type === 0"
                :src="row.resourceUrl"
                style="width: 100px; height: 60px; object-fit: cover"
                muted
                preload="metadata"
              />
              <audio
                v-else-if="row.type === 2"
                :src="row.resourceUrl"
                controls
                style="width: 100px; height: 30px"
                preload="metadata"
              />
              <img
                v-else
                :src="row.resourceUrl"
                style="width: 100px; height: 60px; object-fit: cover"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="类型" width="80">
          <template #default="{ row }">{{
            row.type === 0 ? "视频" : row.type === 2 ? "音频" : "图片"
          }}</template>
        </el-table-column>
        <el-table-column label="描述">
          <template #default="{ row }">
            <el-input
              v-if="isResourceEditable"
              v-model="row.description"
              size="small"
              placeholder="请输入描述"
              clearable
            />
            <span v-else>{{ row.description || "暂无" }}</span>
          </template>
        </el-table-column>

        <el-table-column v-if="isResourceEditable" label="操作" width="100">
          <template #default="{ $index }">
            <el-button
              size="small"
              type="danger"
              @click="removeResource($index)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-dialog v-model="previewDialogVisible" title="资源预览" width="70%">
        <div
          style="
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
          "
        >
          <video
            v-if="previewType === 0"
            :src="previewUrl"
            controls
            style="max-width: 100%; max-height: 70vh; height: auto"
          />
          <audio
            v-else-if="previewType === 2"
            :src="previewUrl"
            controls
            style="width: 100%; max-width: 500px"
          />
          <img
            v-else
            :src="previewUrl"
            style="
              max-width: 100%;
              max-height: 70vh;
              height: auto;
              object-fit: contain;
            "
          />
        </div>
      </el-dialog>

      <template #footer>
        <el-button @click="resourceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmResourceChanges"
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { pageQuery } from "@/api/page";
import dayjs from "dayjs";
import Ajv from "ajv";
import schema from "@/utils/schema.json"; // 你上传的 schema 文件
import DetailDialog from "@/components/DetailDialog.vue";
import { emptyDetailsTemplate } from "@/utils/emptyDetailsTemplate";
import {
  fetchHeritageProjectDetail,
  updateHeritageProject,
  createHeritageProject,
  deleteHeritageProject,
  deleteHeritageProjectsBatch,
} from "@/api/heritageProject"; // 路径根据你实际的接口定义文件调整
import axios from "axios"; // 确保已引入axios

const formatTime = (time) => {
  return time ? dayjs(time).format("YYYY-MM-DD HH:mm:ss") : "暂无";
};

const categoryList = [
  "民间文学",
  "传统音乐",
  "传统舞蹈",
  "传统戏剧",
  "曲艺",
  "体育、游艺与杂技",
  "传统美术",
  "传统技艺",
  "传统医药",
  "民俗",
];
const regionList = [
  "东胜区",
  "康巴什区",
  "达拉特旗",
  "准格尔旗",
  "伊金霍洛旗",
  "鄂托克旗",
  "鄂托克前旗",
  "杭锦旗",
  "乌审旗",
];

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectLevel: "",
  category: "",
  region: "",
  name: "",
});
const tableData = ref([]);
const total = ref(0);

const dialogVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();
const formData = ref({
  id: null,
  name: "",
  projectLevel: "",
  category: "",
  region: "",
  inheritor: "",
  content: "",
  src: "",
  details: {}, // 预留给详情数据
  resourceList: [],
});
const detail = ref({}); // 详情弹窗数据副本
const rules = reactive({
  name: [
    { required: true, message: "请输入项目名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度应为 2~50 个字符", trigger: "blur" },
  ],
  projectLevel: [
    { required: true, message: "请选择项目等级", trigger: "change" },
  ],
  category: [{ required: true, message: "请选择项目类别", trigger: "change" }],
  region: [{ required: true, message: "请选择地区", trigger: "change" }],
  content: [{ max: 1000, message: "简介最多 1000 字", trigger: "blur" }],
  src: [{ required: true, message: "请上传文件", trigger: "change" }],
});

const fetchData = async () => {
  const res = await pageQuery(queryParams.value);
  tableData.value = res.data.records;
  total.value = res.data.total;
};

const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    projectLevel: "",
    category: "",
    region: "",
    name: "",
  };
  fetchData();
};

// 打开新增/编辑弹窗时
const openDialog = (row = null) => {
  isEdit.value = !!row;
  formData.value = row
    ? { ...row, details: row.details || {} }
    : {
        name: "",
        projectLevel: "",
        category: "",
        region: "",
        inheritor: "",
        content: "",
        src: "",
        details: JSON.parse(JSON.stringify(emptyDetailsTemplate)), // 使用模板
        resourceList: [],
      };
  dialogVisible.value = true;
};

const edit = async (row) => {
  isEdit.value = true;
  try {
    const res = await fetchHeritageProjectDetail(row.id); // 获取完整详情
    if (res.success && res.data) {
      // 1. 设置表单字段
      const data = res.data;
      formData.value = {
        id: data.id,
        name: data.name,
        projectLevel: data.projectLevel,
        category: data.category,
        region: data.region,
        inheritor: data.inheritor,
        content: data.content,
        src: data.src,
        details: data.details || {}, // 💡 重点：存入完整 details，供详情弹窗使用
        resourceList: data.resourceList || [], // 若你后续有资源管理
      };
      dialogVisible.value = true;
    }
  } catch (e) {
    ElMessage.error("加载项目数据失败");
  }
};

const remove = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认删除「${row.name}」这个非遗项目吗？`,
      "删除确认",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    await deleteHeritageProject(row.id); // ✅ 用你定义的单条删除接口
    ElMessage.success("删除成功");

    fetchData(); // 重新加载表格
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
      console.error(error);
    }
  }
};

const submitForm = async () => {
  formRef.value.validate(async (valid) => {
    if (!valid) return;

    // 1. 先校验 details 字段是否符合 schema
    const ajv = new Ajv({ allErrors: true });
    const validate = ajv.compile(schema);
    formData.value.details = fixDetailsBeforeValidation(formData.value.details);
    const validDetails = validate(formData.value.details);
    console.log("详情校验结果：", JSON.stringify(formData.value.details));
    if (!validDetails) {
      console.error("详情校验失败：", validate.errors);
      ElMessage.error("详情字段不符合要求，请完善信息后再提交");
      return;
    }

    // 2. 准备提交数据
    const submitData = {
      ...formData.value,
      details: formData.value.details || {},
      resourceList: formData.value.resourceList || [],
    };

    // 3. 根据 isEdit 判断是新增还是修改
    try {
      let res;
      if (isEdit.value) {
        res = await updateHeritageProject(submitData);
        ElMessage.success(res.message || "更新成功");
      } else {
        res = await createHeritageProject(submitData);
        ElMessage.success(res.message || "新增成功");
      }

      // 4. 操作成功后，关闭弹窗并刷新数据
      dialogVisible.value = false;
      fetchData();
    } catch (error) {
      console.error("提交失败：", error);
      ElMessage.error("提交失败，请稍后重试");
    }
  });
};
// 校验前格式修正函数
function fixDetailsBeforeValidation(details) {
  if (Array.isArray(details.involved_ethnic_groups)) {
    details.involved_ethnic_groups = details.involved_ethnic_groups.join("、");
  }

  if (details.funding_for_protection?.funding_sources) {
    details.funding_for_protection.funding_sources = [
      ...new Set(
        details.funding_for_protection.funding_sources.map((source) => {
          switch (source) {
            case "政府投入":
              return "government_input";
            case "社会捐赠":
              return "social_donations";
            case "市场营收":
              return "market_revenue";
            case "其他":
              return "others";
            default:
              return source;
          }
        })
      ),
    ];
  }

  // 补丁：total_amount 强制转为数字
  if (typeof details.funding_for_protection?.total_amount === "string") {
    details.funding_for_protection.total_amount = Number(
      details.funding_for_protection.total_amount
    );
  }

  if (
    details.inheritance_status?.age_structure_of_inheritance_group ===
    "middle_and_old_age"
  ) {
    details.inheritance_status.age_structure_of_inheritance_group =
      "mostly_middle_aged_and_elderly";
  }

  if (!details.promotion_and_display_status) {
    details.promotion_and_display_status = {};
  }

  details.promotion_and_display_status.has_promotion_and_display_activities ??= false;
  details.promotion_and_display_status.frequency_change_of_activities ??=
    "maintained";
  details.promotion_and_display_status.media_attention ??= "low";

  if (!details.promotion_and_display_status.into_school_situation) {
    details.promotion_and_display_status.into_school_situation = {};
  }

  details.promotion_and_display_status.into_school_situation.included_in_curriculum ??= false;
  details.promotion_and_display_status.into_school_situation.included_in_textbooks ??= false;
  details.promotion_and_display_status.into_school_situation.has_campus_activities ??= false;

  return details;
}

// 上传相关变量
const uploadProgress = ref(0);
const isUploading = ref(false);
const currentUploadFile = ref(null);
const cancelTokenSource = ref(null);
const chunkSize = 5 * 1024 * 1024; // 5MB 分片大小，与后端保持一致

// 修改上传URL，使用后端提供的接口
const uploadUrl = "/api/admin/upload";

// 封面上传前的处理函数
function beforeUploadImageOnly(file) {
  const isImage = file.type.startsWith("image/");
  if (!isImage) {
    ElMessage.error("仅允许上传图片文件");
    return false;
  }

  // 移除文件大小限制
  // 开始上传流程
  handleFileUpload(file, "cover");
  return false; // 阻止默认上传
}

// 资源上传前的处理函数
function beforeUploadWithTypeCheck(file) {
  const type = newResource.value?.type;
  const isImage = file.type.startsWith("image/");
  const isVideo = file.type.startsWith("video/");
  const isAudio = file.type.startsWith("audio/");

  if (type === 1 && !isImage) {
    ElMessage.error("资源类型为图片，只能上传图片");
    return false;
  }
  if (type === 0 && !isVideo) {
    ElMessage.error("资源类型为视频，只能上传视频");
    return false;
  }
  if (type === 2 && !isAudio) {
    ElMessage.error("资源类型为音频，只能上传音频文件");
    return false;
  }

  // 移除文件大小限制
  // 开始上传流程
  handleFileUpload(file, "resource");
  return false; // 阻止默认上传
}

// 文件上传处理函数
async function handleFileUpload(file, type) {
  if (isUploading.value) {
    ElMessage.warning("有文件正在上传中，请等待完成");
    return;
  }

  isUploading.value = true;
  uploadProgress.value = 0;
  currentUploadFile.value = { file, type };

  try {
    // 创建取消令牌
    cancelTokenSource.value = axios.CancelToken.source();

    // 创建FormData对象
    const formData = new FormData();
    formData.append("file", file);

    // 发送上传请求
    const response = await axios.post(uploadUrl, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      cancelToken: cancelTokenSource.value.token,
      onUploadProgress: (progressEvent) => {
        // 计算上传进度
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        uploadProgress.value = percentCompleted;
      },
    });

    // 处理上传成功
    if (response.data && response.data.success) {
      const url = response.data.data;

      if (type === "cover") {
        formData.value.src = url;
        ElMessage.success("封面上传成功");
      } else if (type === "resource") {
        newResource.value.resourceUrl = url;
        previewUrl.value = url;
        previewType.value = newResource.value.type;
        ElMessage.success("资源上传成功");
      }
    } else {
      ElMessage.error(response.data?.message || "上传失败");
    }
  } catch (error) {
    if (axios.isCancel(error)) {
      ElMessage.info("上传已取消");
    } else {
      console.error("上传失败:", error);
      ElMessage.error("上传失败，请重试");
    }
  } finally {
    isUploading.value = false;
    currentUploadFile.value = null;
    cancelTokenSource.value = null;
  }
}

// 取消上传
function cancelUpload() {
  if (isUploading.value && cancelTokenSource.value) {
    cancelTokenSource.value.cancel("用户取消上传");
    isUploading.value = false;
    uploadProgress.value = 0;
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  if (isUploading.value && cancelTokenSource.value) {
    cancelTokenSource.value.cancel("组件卸载");
  }
});

// 进度条格式化函数
function percentFormat(percent) {
  return percent === 100 ? "上传完成" : `${percent}%`;
}

function handleCoverUploadSuccess(res) {
  const url = res.data;
  if (!url) {
    ElMessage.error("上传失败");
    return;
  }
  formData.value.src = url;
  ElMessage.success("封面上传成功");
}

function handleResourceUploadSuccess(res) {
  const url = res.data;
  if (!url) {
    ElMessage.error("上传失败");
    return;
  }
  newResource.value.resourceUrl = url;
  previewUrl.value = url;
  previewType.value = newResource.value.type;
  ElMessage.success("资源上传成功");
}

const viewUploadedFile = () => {
  if (formData.value.src) {
    window.open(formData.value.src, "_blank");
  }
};
// 项目详情
const detailDialogVisible = ref(false);

// 打开项目详情弹窗，传递当前表单 details
function openDetailDialog() {
  detail.value = JSON.parse(JSON.stringify(formData.value.details || {})); // 深拷贝，防止修改直接影响表单
  detailDialogVisible.value = true;
}
// 详情弹窗点击保存，更新表单中的 details 数据（仅更新本地，不提交接口）
function handleDetailSave(updatedDetails) {
  formData.value.details = updatedDetails;
  detailDialogVisible.value = false;
}
const viewDetail = async (row) => {
  const res = await fetchHeritageProjectDetail(row.id);
  console.log("接口返回res:", res);
  detail.value = res.data || {};
  console.log("赋值后detail.value:", detail.value);
  detailDialogVisible.value = true;
  console.log("弹窗显示状态:", detailDialogVisible.value);
};
const resourceDialogVisible = ref(false);
const tempResourceList = ref([]);
// const uploadUrl = "/admin/upload"; // 👈 替换成你的上传接口
const isResourceEditable = ref(true); // 是否允许编辑资源

async function viewResources(row) {
  const res = await fetchHeritageProjectDetail(row.id);
  if (res.success && res.data) {
    tempResourceList.value = res.data.resourceList || [];
  } else {
    tempResourceList.value = [];
    ElMessage.warning("未能加载资源");
  }
  isResourceEditable.value = false;
  resourceDialogVisible.value = true;
}

const newResource = ref({
  type: 1, // 默认图片
  description: "",
  resourceUrl: "",
});

// 打开资源弹窗（新增/编辑模式）
function openResourceDialog() {
  tempResourceList.value = JSON.parse(
    JSON.stringify(formData.value.resourceList || [])
  );
  isResourceEditable.value = true;
  resourceDialogVisible.value = true;
  resetNewResource();
}

function confirmResourceChanges() {
  formData.value.resourceList = JSON.parse(
    JSON.stringify(tempResourceList.value)
  );
  resourceDialogVisible.value = false;
}

function addResource() {
  if (!newResource.value.resourceUrl) {
    ElMessage.warning("请先上传资源");
    return;
  }
  tempResourceList.value.push({ ...newResource.value });
  resetNewResource();
}

function removeResource(index) {
  tempResourceList.value.splice(index, 1);
}

function resetNewResource() {
  newResource.value = {
    type: 1,
    description: "",
    resourceUrl: "",
  };
}
const previewDialogVisible = ref(false);
const previewUrl = ref("");
const previewType = ref(1); // 0: 视频, 1: 图片, 2: 音频

function previewResource(row) {
  previewUrl.value = row.resourceUrl;
  previewType.value = row.type;
  previewDialogVisible.value = true;
}
// 批量删除 和批量修改
const multipleSelection = ref([]);

function handleSelectionChange(val) {
  multipleSelection.value = val;
}

async function batchDelete() {
  if (!multipleSelection.value.length) return;

  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${multipleSelection.value.length} 项吗？`,
      "批量删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    const ids = multipleSelection.value.map((item) => item.id);
    await deleteHeritageProjectsBatch(ids); // ✅ 调用批量删除接口
    ElMessage.success("批量删除成功");
    fetchData();
  } catch (error) {
    // 用户取消或接口异常都不提示
  }
}

function batchEdit() {
  if (multipleSelection.value.length !== 1) {
    ElMessage.warning("批量修改仅支持选择一个项目");
    return;
  }
  edit(multipleSelection.value[0]);
}
onMounted(fetchData);
</script>

<style scoped>
.filter-form .el-select {
  min-width: 140px;
}

.filter-form {
  margin-bottom: 20px;
}
.ellipsis-text {
  display: inline-block;
  max-width: 14em; /* 控制宽度 ≈ 15个中文字符 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.section {
  margin-bottom: 20px;
}
</style>
