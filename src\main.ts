import "@/assets/index.css";
import "@/assets/iconfont.css";
import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus";
import piniaPersist from 'pinia-plugin-persistedstate'

const pinia = createPinia()
pinia.use(piniaPersist)          // ★ 没有这行就会报错
const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(ElMessageBox);

app.use(ElementPlus);
app.use(createPinia());
app.use(router);
app.mount("#app");
