<template>
  <el-row class="login-header">
    <el-col :span="24">
      <div class="login-up">
        <img
          class="logo"
          src="@/assets/ordos.jpg"
          style="width: 100px; height: 50px"
        />
        <span class="title">鄂尔多斯非物质文化遗产管理系统</span>
      </div>
    </el-col>
  </el-row>

  <el-row class="login-center">
    <el-col :span="12"></el-col>
    <el-col :span="6" :offset="3" class="form">
      <el-card class="box-card">
        <el-form
          :model="form"
          ref="formRef"
          :rules="rules"
          label-width="80px"
          style="padding-top: 30px"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="form.username"
              placeholder="请输入用户名"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleLogin">登录</el-button>
            <!-- <el-button type="success" @click="showRegister = true" plain>注册</el-button> -->
            <!-- 不应该实现注册 -->
            <el-button type="success" plain>注册</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </el-col>
  </el-row>

  <el-row class="login-footer">
    <el-col :span="24">
      <div class="login-down">
        <p>Copyright © 2024 - 版权归心若灵犀所有</p>
      </div>
    </el-col>
  </el-row>

  <!-- 注册弹窗 -->
  <el-dialog v-model="showRegister" title="用户注册" width="400px">
    <el-form
      :model="registerForm"
      ref="registerFormRef"
      :rules="registerRules"
      label-width="80px"
    >
      <el-form-item label="用户名" prop="username">
        <el-input v-model="registerForm.username" placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item label="密码" prop="pwd">
        <el-input
          v-model="registerForm.pwd"
          type="password"
          placeholder="请输入密码"
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirm">
        <el-input
          v-model="registerForm.confirm"
          type="password"
          placeholder="请确认密码"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showRegister = false">取消</el-button>
      <el-button type="primary" @click="handleRegister">注册</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { login, register } from "@/api/login";
import { useTokenStore } from "@/stores/useTokenStore.js";
import { useUserStore } from "@/stores/useUserStore.js";

const router = useRouter();
const tokenStore = useTokenStore();
const userStore = useUserStore();

const form = ref({
  username: "",
  password: "",
});

const rules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
};

function handleLogin() {
  const { username, password } = form.value;
  login({ username, password })
    .then((response) => {
      if (response.success) {
        const { token, user } = response.data;
        tokenStore.setToken(token);
        userStore.setUser({
          id: user.id,
          username: user.username,
          avatar: user.avatar || "https://i.pravatar.cc/100",
        });
        window.open("/admin", "_blank");
      } else {
        ElMessage.error(response.message || "登录失败");
      }
    })
    .catch(() => {
      ElMessage.error("登录请求失败，请稍后重试");
    });
}

// 注册逻辑
const showRegister = ref(false);
const registerForm = ref({
  username: "",
  pwd: "",
  confirm: "",
});

const registerRules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  pwd: [{ required: true, message: "请输入密码", trigger: "blur" }],
  confirm: [
    { required: true, message: "请确认密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.value.pwd) {
          callback(new Error("两次密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

function handleRegister() {
  const { username, pwd, confirm } = registerForm.value;
  if (pwd !== confirm) {
    ElMessage.error("两次密码不一致");
    return;
  }
  register({ username, pwd }).then((res) => {
    if (res.success) {
      ElMessage.success("注册成功，请登录");
      showRegister.value = false;
    } else {
      ElMessage.error(res.message || "注册失败");
    }
  });
}
</script>

<style scoped>
.login-container {
  max-width: 300px;
  margin: 100px auto;
}

.login-header {
  height: 80px;
  background-color: white;
  .login-up {
    margin: 20px 0;
    height: 40px;
    display: flex;
    padding-left: 100px;
  }
  .title {
    line-height: 40px;
    font-size: 18px;
    font-weight: 500;
    color: #999999;
    padding-left: 5px;
    border-left: 2px solid #999999;
  }
}

.login-center {
  background: url("@/assets/photo/login_bg.jpg") no-repeat;
  background-size: cover;
  height: 100vh;
  .box-card {
    padding: 20px;
    background-color: white;
    border-radius: 10px;
  }
  .form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    user-select: none;
    .title {
      margin: 0 auto;
    }
    .button {
      width: 100%;
    }
    .flex {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }
}

.login-footer {
  height: 20px;
  .login-down p {
    text-align: center;
    font-size: 16px;
    font-family: Microsoft YaHei;
    color: #777777;
    line-height: 22px;
  }
}
</style>
