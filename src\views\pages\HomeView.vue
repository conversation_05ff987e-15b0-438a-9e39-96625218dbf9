<template>
  <div style="margin-top: 0px; background-color: #e6e6e6">
    <el-carousel height="100vh">
      <el-carousel-item v-for="item in carousel" :key="item.src">
        <img :src="item.src" :alt="item.src" />
      </el-carousel-item>
    </el-carousel>
    <!-- <jiangxi-map /> -->
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
// import JiangxiMap from "@/components/JiangxiMap.vue";

const carousel = ref([
  {
    src: new URL("@/assets/photo/1527211875348729.jpg", import.meta.url).href,
  },
  {
    src: new URL("@/assets/photo/11111111.png", import.meta.url).href,
  },
  {
    src: new URL("@/assets/photo/c969-ipvnsze7035771.jpg", import.meta.url)
      .href,
  },
  {
    src: new URL("@/assets/photo/ordosHunli.jpg", import.meta.url).href,
  },
  {
    src: new URL("@/assets/photo/MingDiao.jpg", import.meta.url).href,
  },
  {
    src: new URL("@/assets/photo/Xiusi.jpg", import.meta.url).href,
  },
]);
</script>

<style scoped></style>
