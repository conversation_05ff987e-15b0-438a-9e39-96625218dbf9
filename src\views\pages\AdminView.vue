<template>
  <el-container class="admin-layout">
    <!-- 左侧导航栏 -->
    <el-aside width="200px" class="sidebar">
      <div class="logo">非遗后台</div>
      <el-menu router class="menu" :default-active="$route.path">
        <el-menu-item index="/admin/home">后台首页</el-menu-item>
        <el-menu-item index="/admin/projects">非遗项目管理</el-menu-item>
        <el-menu-item index="/admin/articles">文章管理</el-menu-item>
        <el-menu-item index="/admin/users">用户管理</el-menu-item>
        <el-menu-item index="/admin/profile">个人信息</el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主体内容 -->
    <el-container>
      <el-header class="header">
        <div class="header-left">
          <h2>鄂尔多斯非物质文化遗产管理系统</h2>
        </div>
        <div class="header-right">
          <span class="username">{{ username }}</span>
          <el-avatar
            :src="avatarUrl"
            @click="showAvatarDialog = true"
            class="avatar"
          />
          <el-button type="text" @click="confirmLogout" class="logout-btn"
            >退出登录</el-button
          >
        </div>
      </el-header>

      <el-main class="main">
        <router-view />
      </el-main>
    </el-container>

    <!-- 头像预览 -->
    <el-dialog
      v-model="showAvatarDialog"
      width="300px"
      title="头像预览"
      align-center
    >
      <div style="text-align: center">
        <img :src="avatarUrl" style="width: 100%; border-radius: 8px" />
      </div>
    </el-dialog>
  </el-container>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useTokenStore } from "@/stores/useTokenStore.js";
import { useUserStore } from "@/stores/useUserStore.js";
import { ElMessageBox, ElMessage } from "element-plus";
// import axios from "axios"; // 如果需要调用后端 /logout 接口取消 Redis token

const router = useRouter();
const tokenStore = useTokenStore();
const userStore = useUserStore();

const username = computed(() => userStore.username);
const avatarUrl = computed(
  () => userStore.avatar || "https://i.pravatar.cc/100"
);
const showAvatarDialog = ref(false);

const logout = async () => {
  tokenStore.removeToken();
  userStore.clearUser();
  // router.push("/login");
  window.location.href = "/login"; // 强制跳转登录页（适用于新窗口）
};

const confirmLogout = () => {
  ElMessageBox.confirm("确定要退出登录吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      logout();
      ElMessage.success("退出成功");
    })
    .catch(() => {
      ElMessage.info("已取消退出");
    });
};

const goToHome = () => {
  // 在当前窗口跳转到首页
  window.location.href = "/home";
};
</script>

<style scoped>
.admin-layout {
  height: 100vh;
  width: 100vw; /* 使用视口宽度确保占满整个屏幕 */
  overflow-x: hidden; /* 防止水平滚动条 */
}

.sidebar {
  background-color: #2d3a4b;
  color: #fff;
  flex-shrink: 0; /* 防止侧边栏收缩 */
}

.logo {
  height: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  background-color: #1f2d3d;
  color: #fff;
}

.menu {
  border-right: none;
}

.admin-layout .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5f7fa;
  padding: 0 10px;
  height: 60px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left .home-btn {
  background-color: #409eff;
  border-color: #409eff;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.header-left .home-btn:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  letter-spacing: 0.5px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right .username {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.header-right .avatar {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.header-right .avatar:hover {
  transform: scale(1.05);
}

.header-right .logout-btn {
  color: #409eff;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.header-right .logout-btn:hover {
  background-color: #e6f7ff;
}

.admin-layout .main {
  background-color: #f0f2f5;
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden; /* 防止内容溢出 */
  min-height: auto; /* 重置 App.vue 中的 min-height */
  height: auto; /* 重置 App.vue 中的 height */
}

/* 重置 Element Plus 默认样式 */
:deep(.el-container) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.el-header) {
  width: 100% !important;
  height: 60px !important;
}

:deep(.el-main) {
  width: 100% !important;
  padding: 0 !important;
  flex: 1 !important;
}

:deep(.el-aside) {
  width: 200px !important;
  flex-shrink: 0 !important;
}
</style>
