<template>
  <div style="padding: 40px">
    <quill-editor
      v-model:content="content"
      content-type="html"
      style="height: 300px"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { QuillEditor } from "@vueup/vue-quill";
import "@vueup/vue-quill/dist/vue-quill.snow.css";

const content = ref("<p>欢迎使用 Quill 编辑器</p>");
</script>

<style scoped>
body {
  margin: 0;
  font-family: Arial, sans-serif;
}
</style>
