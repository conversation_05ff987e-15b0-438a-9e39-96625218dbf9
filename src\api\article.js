import request from '@/request'
export function fetchArticlePage(data) {
    return request({
      url: '/article/page',
      method: 'post',
      data
    })
  }
  
  // 获取文章详情
  export function fetchArticleDetail(id) {
    return request({
      url: `/article/detail/${id}`,
      method: 'get'
    })
  }
  
  // 新增文章
  export function addArticle(data) {
    return request({
      url: '/article/add',
      method: 'post',
      data
    })
  }
  
  // 更新文章
  export function updateArticle(data) {
    return request({
      url: '/article/update',
      method: 'put',
      data
    })
  }
  
  // 删除文章（单个）
  export function deleteArticle(id) {
    return request({
      url: `/article/delete/${id}`,
      method: 'delete'
    })
  }
  
  // 批量删除文章
  export function deleteArticles(ids) {
    return request({
      url: '/article/deleteBatch',
      method: 'delete',
      data: ids
    })
  }

  // 更新文章状态（草稿 <-> 发布）
export function updateArticleStatus(id, status) {
  return request({
    url: `/article/status/${id}`,
    method: 'put',
    params: { status }
  });
}
