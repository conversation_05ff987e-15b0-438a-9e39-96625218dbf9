{"name": "vite-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueup/vue-quill": "^1.2.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ajv": "^8.17.1", "axios": "^1.6.2", "echarts": "^5.4.3", "element-plus": "^2.5.5", "js-tokens": "^9.0.1", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^2.4.0", "quill": "^1.3.7", "vue": "^3.3.11", "vue-router": "^4.2.5"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/geojson": "^7946.0.14", "@types/node": "^18.19.3", "@types/quill": "^2.0.14", "@vitejs/plugin-vue": "^4.5.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/tsconfig": "^0.5.0", "npm-run-all2": "^6.1.1", "typescript": "^5.7.2", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vue-tsc": "^2.1.10"}}