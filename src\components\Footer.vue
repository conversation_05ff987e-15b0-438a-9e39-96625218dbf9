<template>
  <div
    class="footer"
    @mousemove="handleMouseMove"
    @mouseleave="resetBars"
    style="margin-bottom: 0%"
  >
    <div class="divider"></div>
    <div class="footer-links">
      <a href="#">网站地图</a> | <a href="#">关于我们</a> |
      <a href="#">联系我们</a> |
      <a href="#">版权与免责声明</a>
    </div>
    <!-- 新增：友情链接 -->
    <div class="friend-links">
      友情链接：
      <a href="https://ordoswh.cn/" target="_blank" rel="noopener"
        >鄂尔多斯文化大数据 ①</a
      >
      |
      <a href="https://www.oit.edu.cn/" target="_blank" rel="noopener"
        >鄂尔多斯应用技术学院 ②</a
      >
    </div>
    <p>建议使用360极速、Chrome、Firefox浏览器，最佳分辨率1920×1080</p>
    <p style="display: inline-block">
      Copyright ©2018-2022 ordoswh.cn 鄂尔多斯市文化和旅游局
      <a href="http://www.beian.miit.gov.cn/" target="_blank"
        >蒙ICP备09003096号-7</a
      >
    </p>
    <!-- 动态柱状条 -->
    <div class="bar-container">
      <div
        v-for="(bar, index) in bars"
        :key="index"
        :style="{ height: bar.height + 'px', backgroundColor: bar.color }"
        class="bar"
      ></div>
    </div>
  </div>
</template>

<script setup name="Footer" lang="ts">
import { ref } from "vue";
// 随机颜色生成函数
const randomColor = () => {
  const colors = [
    "#2E3A46",
    "#42575A",
    "#5C5A56",
    "#4A3928",
    "#645B52",
    "#59595A",
    "#32444A",
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

// 计算 bars 数组，根据窗口宽度初始化
const bars = ref(
  Array.from({ length: Math.floor(window.innerWidth / 52) }, () => ({
    height: 50,
    color: randomColor(),
  }))
);

// 鼠标移动时动态更新柱状条高度
/** 鼠标移动时动态调整柱状条高度 */
const handleMouseMove = (event: MouseEvent): void => {
  const { clientX } = event;
  const barWidth = window.innerWidth / bars.value.length;
  const barIndex = Math.floor(clientX / barWidth);

  bars.value = bars.value.map((bar, index) => {
    const distance = Math.abs(index - barIndex);
    const height = Math.max(50, 100 - distance * 10);
    return { ...bar, height };
  });
};

// 重置柱状条高度
const resetBars = () => {
  bars.value = bars.value.map((bar) => ({
    ...bar,
    height: 50,
  }));
};
</script>

<style scoped>
.footer {
  text-align: center;
  padding: 20px;
  font-size: 12px;
  background-color: #f5f5f5;
  color: #555;
  /* 为柱状条留出空间 */
}

.footer-links {
  margin-bottom: 10px;
}

.footer-links a {
  color: #888;
  text-decoration: none;
  margin: 0 5px;
}

.footer-links a:hover {
  text-decoration: underline;
}

/* 其他样式保持原样 */
.bar-container {
  display: flex;
  align-items: flex-end;
  height: 100px;
  margin-top: 10px;
  padding-bottom: 10px;
}

.bar {
  width: 80px;
  /* margin: 0 2px; */
  transition: height 0.3s ease;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #ccc;
  margin: 20px 0;
}
</style>
