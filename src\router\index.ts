import { createRouter, createWebHistory } from "vue-router";
import HeritageProjectDetail from '../views/pages/HeritageProjectDetail.vue';
import { useTokenStore } from "@/stores/useTokenStore.js";

// 定义路由
const routes = [
    {
        path: "/login",
        name: "登录",
        component: () => import("../views/LoginView.vue"),
    },
    {
        path: "/",
        name: "layout",
        redirect: "/home",
        component: () => import("../views/LayoutView.vue"),
        children: [
            {
                path: "/home",
                name: "首页",
                component: () => import("../views/pages/HomeView.vue"),
            },
            {
                path: "/resources",
                name: "资源",
                component: () => import("../views/pages/AssetsView.vue"),
            },
            {
                path: '/detail/:id',
                name: 'HeritageProjectDetail',
                component: HeritageProjectDetail,
                props: true
            },
            {
                path: "/map",
                name: "地图",
                component: () => import("../views/pages/MapDataView.vue"),
            },
            {
                path: "/article",
                name: "文章列表",
                component: () => import("../views/pages/ArticleList.vue"),
            },
            {
                path: "/article/:id",
                name: "文章详情",
                component: () => import("../views/pages/ArticleDetail.vue"),
                props: true
            },
        ],
    },
    // 独立的后台管理路由，不包含网站通用的header和footer
    {
        path: "/admin",
        name: "管理员",
        component: () => import("../views/pages/AdminView.vue"),
        meta: { requiresAuth: true }, // 需要认证
        redirect: "/admin/home", // 默认重定向到后台首页
        children: [
            {
                path: "/admin/home",
                name: "后台首页",
                component: () => import("../views/admin/AdminHome.vue"),
                meta: { requiresAuth: true }
            },
            {
                path: "/admin/projects",
                name: "非遗项目管理",
                component: () => import("../views/admin/AdminProjects.vue"),
                meta: { requiresAuth: true }
            },
            {
                path: "/admin/articles",
                name: "文章管理",
                component: () => import("../views/admin/AdminArticles.vue"),
                meta: { requiresAuth: true }
            },
            {
                path: "/admin/users",
                name: "用户管理",
                component: () => import("../views/admin/AdminUsers.vue"),
                meta: { requiresAuth: true }
            },
            {
                path: "/admin/profile",
                name: "个人信息",
                component: () => import("../views/admin/AdminProfile.vue"),
                meta: { requiresAuth: true }
            }
        ]
    },
];

// 创建路由实例
const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes,
});

// 全局路由守卫：检查是否需要认证
router.beforeEach((to, _from, next) => {
    const useTokenstore = useTokenStore();
    const token = useTokenstore.token;

    // 如果目标路由需要认证并且没有 token，重定向到登录页
    if (to.matched.some(record => record.meta.requiresAuth) && !token) {
        next("/login");
    } else {
        next();
    }
});

export default router;
