import { defineStore } from "pinia";
import { ref } from "vue";

export const useUserStore = defineStore("user", () => {
  const id = ref(localStorage.getItem("id") || "");
  const username = ref(localStorage.getItem("username") || "");
  const avatar = ref(localStorage.getItem("avatar") || "");

  const setUser = (user) => {
    id.value = user.id;
    username.value = user.username;
    avatar.value = user.avatar;
    localStorage.setItem("id", user.id);
    localStorage.setItem("username", user.username);
    localStorage.setItem("avatar", user.avatar);
  };
  const setAvatar = (url) => {
    avatar.value = url;
    localStorage.setItem("avatar", url);
  };
  
  const clearUser = () => {
    id.value = "";
    username.value = "";
    avatar.value = "";
    localStorage.removeItem("id");
    localStorage.removeItem("username");
    localStorage.removeItem("avatar");
  };

  return {
    id,
    username,
    avatar,
    setUser,
    clearUser,
    setAvatar
  };
});
