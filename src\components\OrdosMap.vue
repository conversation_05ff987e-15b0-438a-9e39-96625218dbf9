<template>
  <div
    class="w-100vw h-100vh"
    style="
      background: url(https://www.ihchina.cn/Uploads/Picture/2019/10/11/s5da048b1840e2.jpg)
        no-repeat center/100% 100%;
      margin-top: 0px;
    "
  >
    <!-- 顶部标题 -->
    <div class="h-10vh">
      <div class="flex align-center justify-center line-h-10vh">
        <div
          class="iconfont icon-knot text-bold text-white padding-lr-xs"
        ></div>
        <div
          class="ff text-bold text-white text-center text-df text-overflow-sub"
        >
          非物质文化遗产地图
        </div>
        <div
          class="iconfont icon-knot text-bold text-white padding-lr-xs"
        ></div>
      </div>
    </div>

    <!-- 中间图表和数据展示 -->
    <div class="h-90vh flex align-center justify-center">
      <!-- 左侧地图 -->
      <div ref="chartContainer" class="w-50vw h-90vh"></div>

      <!-- 右侧数据展示 -->
      <div
        class="padding-sm w-25vw h-65vh text-white text-center"
        style="background-color: dimgrey"
      >
        <div class="text-xs margin-top-xxxs text-overflow-sub">
          <!-- 查看全市按钮 -->
          <button
            @click="resetToCityData"
            v-if="city !== '鄂尔多斯市'"
            class="view-city-btn"
          >
            查看全市
          </button>
          【 {{ city }} 】
        </div>
        <div
          class="flex align-center justify-center border-bottom-da padding-xs margin-top-xs text-xxxs cursor"
        >
          <div
            @click="filterByLevel('全部')"
            :class="['level-btn', { 'active-level': activeLevel === '全部' }]"
          >
            全部
          </div>
          <div
            @click="filterByLevel('国家级')"
            :class="['level-btn', { 'active-level': activeLevel === '国家级' }]"
          >
            国家级
          </div>
          <div
            @click="filterByLevel('市级')"
            :class="['level-btn', { 'active-level': activeLevel === '市级' }]"
          >
            市级
          </div>
          <div
            @click="filterByLevel('区县级')"
            :class="['level-btn', { 'active-level': activeLevel === '区县级' }]"
          >
            区县级
          </div>
        </div>

        <div class="margin-top-xxs">
          <!-- 分类项目展示 -->
          <div>
            <div
              class="bg-tran w-33 fl padding-xxxs"
              v-for="(item, index) in filteredList"
              :key="index"
            >
              <div
                class="bg-brown h-fill padding-lr-sm h-80 padding-tb-xs"
                :class="
                  index === filteredList.length - 1 ? 'last-category' : ''
                "
              >
                <div class="text-lg text-bold ff-impact">
                  {{ item.count }}
                </div>
                <div class="text-xxxxs margin-top-xxxs text-overflow-twice">
                  {{ item.label }}
                </div>
              </div>
            </div>
          </div>

          <div class="text-xxxs margin-top-sm fl line-h-20 text-overflow-twice">
            说明：数据来自国家文化和旅游行政主管部门公开信息数据截至2024年06月30日。
          </div>

          <div class="fl w-fill flex align-center justify-center margin-top-xs">
            <div class="iconfont icon-clound text-xl"></div>
            <div class="text-xxs padding-lr-xs">
              总计:
              <span class="text-sm ff-impact text-bold-2">{{
                totalProjects
              }}</span>
              项
            </div>
            <div class="iconfont icon-clound text-xl"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { getList } from "@/api/CountData";
import jiangxi from "@/assets/jiangxi.json";
import * as echarts from "echarts/core";
import { MapChart } from "echarts/charts";
import { TooltipComponent, GeoComponent } from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

echarts.use([MapChart, TooltipComponent, GeoComponent, CanvasRenderer]);

// 定义类型接口
interface CategoryStat {
  label: string;
  count: number;
  projectLevel: string;
}

interface ProjectItem {
  region: string;
  projectLevel: string;
  category: string;
}

interface ApiResp<T> {
  code: number;
  data: T;
  message: string;
}

const city = ref("鄂尔多斯市");
const list = ref<CategoryStat[]>([]);
const levelFilter = ref("全部");
const mapData = jiangxi as any;
const chartContainer = ref<HTMLElement | null>(null);

const categories: readonly string[] = [
  "民间文学",
  "传统音乐",
  "传统舞蹈",
  "传统戏剧",
  "曲艺",
  "传统美术",
  "传统技艺",
  "传统医药",
  "民俗",
  "体育、游艺与杂技",
];
// 固定类别顺序
const fixedCategoryOrder: string[] = [
  "民俗",
  "民间文学",
  "传统音乐",
  "传统舞蹈",
  "传统戏剧",
  "曲艺",
  "传统美术",
  "传统技艺",
  "传统医药",
  "体育、游艺与杂技",
];
// 根据选择的项目等级筛选分类项目
// 根据选择的项目等级筛选分类项目并按固定顺序排序
const filteredList = computed<CategoryStat[]>(() => {
  const filtered =
    levelFilter.value === "全部"
      ? list.value
      : list.value.filter(
          (i) =>
            i.projectLevel === levelFilter.value ||
            (levelFilter.value === "区县级" && i.projectLevel === "自治区级")
        );

  // 按固定顺序排序
  return fixedCategoryOrder.map((category) => {
    const item = filtered.find((i) => i.label === category);
    return item ? item : { label: category, count: 0, projectLevel: "全部" };
  });
});

// 计算总数
const totalProjects = computed(() =>
  filteredList.value.reduce((s, i) => s + i.count, 0)
);

let chart: echarts.ECharts | null = null;

// ----------------------------------------------------------------------------
//  获取并统计非遗项目数据
// ----------------------------------------------------------------------------
async function fetchProjectData(region: string = "鄂尔多斯市"): Promise<void> {
  try {
    const raw = await getList();

    // ↓ 判断 raw 是否形如 { success: boolean, data: … }
    const resp: ApiResp<ProjectItem[]> =
      raw &&
      typeof raw === "object" &&
      "code" in raw &&
      "data" in raw &&
      "message" in raw
        ? (raw as ApiResp<ProjectItem[]>)
        : {
            code: 200,
            message: "ok",
            data: raw as unknown as ProjectItem[],
          };

    // 2) 校验接口状态 ------------------------------------------------------
    if (resp.code !== 200) {
      console.error("数据获取失败：", resp.message);
      list.value = []; // 清空旧数据
      return;
    }

    //----------------------------------------------------------------------
    // 3) 统计分类数量
    //----------------------------------------------------------------------
    // 3-1 先为十一个分类准备“计数桶”
    const counts: CategoryStat[] = categories.map((label) => ({
      label,
      count: 0,
      projectLevel: "全部",
    }));

    // 3-2 遍历全部项目，按【地区 ─ 分类 ─ 级别】累计
    resp.data.forEach((p) => {
      // a) 地区过滤：全市或子地区命中即可
      const hitRegion = region === "鄂尔多斯市" || p.region.includes(region);
      if (!hitRegion) return;

      // b) 级别映射：后端“自治区级”在界面算作“区县级”
      const lvl = p.projectLevel === "自治区级" ? "区县级" : p.projectLevel;

      // c) 找到分类桶
      const bucket = counts.find((c) => c.label === p.category);
      if (!bucket) return;

      // d) 结合当前筛选条件计数
      if (levelFilter.value === "全部" || lvl === levelFilter.value) {
        bucket.count++;
        bucket.projectLevel = lvl; // 记录真实级别，便于后续再次筛选
      }
    });

    // 确保每个类别即使没有项目也会显示
    categories.forEach((category) => {
      const existingCategory = counts.find((c) => c.label === category);
      if (!existingCategory) {
        counts.push({ label: category, count: 0, projectLevel: "全部" });
      }
    });

    // 4) 写回响应式列表，触发界面刷新 -----------------------------
    list.value = counts;
  } catch (err) {
    console.error("请求异常：", err);
    list.value = [];
  }
}

// 设置筛选级别
const activeLevel = ref("全部");
function filterByLevel(level: string): void {
  activeLevel.value = level;
  levelFilter.value = level;
  fetchProjectData(city.value);
}

// 初始化图表
function initChart(): void {
  if (!chartContainer.value) return;
  echarts.registerMap("jiangxi", mapData);
  chart = echarts.init(chartContainer.value);
  chart.setOption({
    backgroundColor: "transparent",
    geo: {
      map: "jiangxi",
      label: { show: true, color: "#000" },
      itemStyle: {
        borderColor: "#fff",
        borderWidth: 1,
        areaColor: {
          type: "radial",
          x: 0.5,
          y: 0.5,
          r: 0.8,
          colorStops: [
            { offset: 0, color: "rgba(255,255,255,.5)" },
            { offset: 1, color: "rgba(255,255,255,.5)" },
          ],
        },
      },
      emphasis: {
        itemStyle: { areaColor: "rgba(255,255,255,.6)", borderWidth: 1 },
      },
    },
    tooltip: {
      trigger: "item",
      formatter: (p: any) => `地区名称:${p.name}<br/>ID:${p.dataIndex}`,
    },
  });
}

// 添加点击事件监听器
function addClickListener(): void {
  chart?.on("click", (p: echarts.ECElementEvent) => {
    if (p.name) {
      city.value = p.name;
      fetchProjectData(p.name);
    }
  });
}

// 重置城市数据
function resetToCityData(): void {
  city.value = "鄂尔多斯市";
  fetchProjectData();
}

onMounted(async () => {
  await fetchProjectData();
  initChart();
  addClickListener();
});

onBeforeUnmount(() => {
  chart?.dispose();
  chart = null;
});
</script>

<style scoped>
.bg-brown {
  background-color: #a89171;
  border-radius: 5px;
  padding: 3px;
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  justify-items: center;
}

.h-fill {
  min-height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.last-category {
  min-height: 80px;
  /* 将最后一个框的高度设置为更大 */
  padding: 5px;
  /* 可调整内边距来适应内容 */
  flex-grow: 3; /* 让最后一项占据更多的空间 */
}

.text-xxxxs {
  font-size: 10px;
  line-height: 1.1;
}

.text-overflow-sub {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  color: #ffedcf;
}

.level-btn {
  padding: 0 10px;
  /* 调整左右间距 */
  color: #fbff07ee;
  /* 未点击时的黄色 */
  cursor: pointer;
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
  font-weight: normal;
}

.active-level {
  color: #00b7ff;
  /* 点击时的蓝色 */
  font-weight: bold;
}

.view-city-btn {
  color: #00b7ff;
  text-decoration: underline;
  font-size: 14px;
  background: none;
  border: none;
  cursor: pointer;
  margin-bottom: 10px;
}

.view-city-btn:hover {
  color: #0099cc;
}
</style>
