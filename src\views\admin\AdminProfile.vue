<template>
  <div>
    <h3>个人信息</h3>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="用户名">
        <el-input v-model="form.username" disabled />
      </el-form-item>

      <el-form-item label="角色">
        <el-input v-model="form.role" disabled />
      </el-form-item>

      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入手机号" />
      </el-form-item>

      <el-form-item label="旧密码" prop="oldPassword">
        <el-input
          v-model="form.oldPassword"
          type="password"
          show-password
          placeholder="请输入旧密码（如需修改密码）"
        />
      </el-form-item>

      <el-form-item label="新密码" prop="password">
        <el-input
          v-model="form.password"
          type="password"
          show-password
          placeholder="留空则不修改"
        />
      </el-form-item>

      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="form.confirmPassword"
          type="password"
          show-password
          placeholder="请再次输入新密码"
        />
      </el-form-item>

      <el-form-item label="头像">
        <el-upload
          class="avatar-uploader"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :on-success="handleAvatarSuccess"
          action="/api/admin/upload"
        >
          <img v-if="form.avatar" :src="form.avatar" class="avatar" />
          <el-icon v-else><Plus /></el-icon>
        </el-upload>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { getCurrentUserInfo, updateSelfInfo } from "@/api/admin";
import { useTokenStore } from "@/stores/useTokenStore";
import { useUserStore } from "@/stores/useUserStore";
import router from "@/router";

const formRef = ref();

const form = ref({
  id: null,
  username: "",
  role: "",
  phone: "",
  oldPassword: "",
  password: "",
  confirmPassword: "",
  avatar: "",
});

const rules = {
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "手机号格式不正确",
      trigger: "blur",
    },
  ],
  password: [{ min: 6, message: "密码至少 6 位", trigger: "blur" }],
  confirmPassword: [
    {
      validator: (rule, value, callback) => {
        if (form.value.password && value !== form.value.password) {
          callback(new Error("两次输入密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith("image/");
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isImage) ElMessage.error("请上传图片文件");
  if (!isLt2M) ElMessage.error("图片大小不能超过2MB");
  return isImage && isLt2M;
};

const userStore = useUserStore();

const handleAvatarSuccess = (res) => {
  form.value.avatar = res.data;
  userStore.setAvatar(res.data); // 更新全局 avatar
};

const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (!valid) return;

    const payload = { ...form.value };
    const isPasswordChanged = !!payload.password;

    if (!isPasswordChanged) {
      delete payload.password;
      delete payload.confirmPassword;
      delete payload.oldPassword;
    } else {
      delete payload.confirmPassword;
    }

    const res = await updateSelfInfo(payload);
    if (res.code === 200) {
      if (isPasswordChanged) {
        ElMessageBox.alert("密码修改成功，请重新登录", "提示", {
          confirmButtonText: "确定",
          type: "success",
          callback: () => {
            useTokenStore().removeToken();
            useUserStore().clearUser();
            window.location.href = "/login";
          },
        });
      } else {
        ElMessage.success("个人信息更新成功");
        if (payload.avatar) {
          useUserStore().setAvatar(payload.avatar);
        }
      }
    } else {
      ElMessage.error(res.message || "更新失败");
    }
  });
};

const loadUserInfo = async () => {
  const res = await getCurrentUserInfo();
  if (res.code === 200) {
    form.value = {
      ...res.data,
      oldPassword: "",
      password: "",
      confirmPassword: "",
    };
  }
};

onMounted(loadUserInfo);
</script>

<style scoped>
.avatar-uploader {
  display: flex;
  align-items: center;
}
.avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
}
</style>
