// src/stores/useIndexStore.ts
import { defineStore } from 'pinia'

interface State {
  selectedKeys: string
  myself: string
}

export const useIndexStore = defineStore('index', {
  state: (): State => ({
    selectedKeys: '/home',
    myself: ''
  }),

  // 👇 如果您用了 pinia-plugin-persistedstate，留着；否则删掉
  persist: true,

  actions: {
    changeSelectedKeys(key: string) {
      this.selectedKeys = key
    },
    changeMyself(val: string) {
      this.myself = val
    }
  }
})
