<template>
  <div class="admin-home">
    <h2>👋 欢迎回来，{{ username }}！</h2>
    <p>这是鄂尔多斯非物质文化遗产管理系统后台首页。</p>

    <!-- 🎞️ 轮播图区域 -->
    <el-carousel :interval="4000" class="carousel">
      <el-carousel-item v-for="(item, index) in banners" :key="index">
        <img :src="item.src" class="banner-img" />
      </el-carousel-item>
    </el-carousel>

    <!-- 📢 公告栏 -->
    <el-alert
      v-if="announcement"
      type="info"
      title="📢 系统公告"
      :description="announcement"
      show-icon
      class="notice"
    />

    <!-- 📊 统计卡片 -->
    <el-row :gutter="20" class="card-group">
      <el-col :span="6">
        <el-card>
          <div class="card-title">非遗项目</div>
          <div class="card-value">{{ stats.projects }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="card-title">文章总数</div>
          <div class="card-value">{{ stats.articles }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="card-title">用户数</div>
          <div class="card-value">{{ stats.users }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 📝 各级别非遗项目统计图 -->
    <div class="section">
      <h3>📊 各级别非遗项目统计</h3>
      <div class="chart-wrapper">
        <div ref="chartContainer" class="chart-container"></div>
      </div>
    </div>

    <!-- 📰 系统动态 -->
    <div class="section">
      <h3>📰 最新发布文章</h3>
      <el-timeline>
        <el-timeline-item
          v-for="item in recentArticles"
          :key="item.id"
          :timestamp="item.createdTime"
        >
          {{ item.title }}
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useUserStore } from "@/stores/useUserStore.js";
import { fetchAdminStats } from "@/api/admin";
import { fetchArticlePage } from "@/api/article";
import { getList } from "@/api/CountData"; // 使用你提供的API
import { pageQuery } from "@/api/page"; // 备用API
import * as echarts from "echarts"; // 导入echarts
/* 静态导入轮播图，文件名可自行对应 */
import banner1 from "@/assets/photo/banner/生成轮播图 (3).png";
import banner2 from "@/assets/photo/banner/生成轮播图 (4).png";
import banner3 from "@/assets/photo/banner/生成轮播图 (5).png";
import banner4 from "@/assets/photo/banner/生成轮播图 (6).png";
const userStore = useUserStore();
const username = computed(() => userStore.username || "管理员");

const stats = ref({ projects: 0, articles: 0, users: 0 });
const chartContainer = ref(null);

const banners = [
  { src: banner1 },
  { src: banner2 },
  { src: banner3 },
  { src: banner4 },
];

const announcement = "6月15日系统维护，请提前保存数据。";

const recentArticles = ref([]);
const categoryStats = ref({
  国家级: {
    民间文学: 0,
    传统音乐: 0,
    传统舞蹈: 0,
    传统戏剧: 0,
    曲艺: 0,
    传统美术: 0,
    传统技艺: 0,
    传统医药: 0,
    民俗: 0,
    "体育、游艺与杂技": 0,
  },
  市级: {
    民间文学: 0,
    传统音乐: 0,
    传统舞蹈: 0,
    传统戏剧: 0,
    曲艺: 0,
    传统美术: 0,
    传统技艺: 0,
    传统医药: 0,
    民俗: 0,
    "体育、游艺与杂技": 0,
  },
  自治区级: {
    民间文学: 0,
    传统音乐: 0,
    传统舞蹈: 0,
    传统戏剧: 0,
    曲艺: 0,
    传统美术: 0,
    传统技艺: 0,
    传统医药: 0,
    民俗: 0,
    "体育、游艺与杂技": 0,
  },
});

onMounted(async () => {
  const statRes = await fetchAdminStats();
  if (statRes.success) stats.value = statRes.data;

  const articleRes = await fetchArticlePage({ pageNum: 1, pageSize: 3 });
  if (articleRes.success) {
    recentArticles.value = articleRes.data.records;
  }

  try {
    console.log("🔍 开始获取项目数据...");

    let statsRes;
    let projectData = [];

    // 尝试使用 getList API
    try {
      statsRes = await getList();
      console.log("📊 getList API 返回的原始数据:", statsRes);

      if (statsRes && (statsRes.success || statsRes.code === 200)) {
        projectData = statsRes.data || [];
      }
    } catch (error) {
      console.warn("⚠️ getList API 调用失败，尝试使用 pageQuery API:", error);
    }

    // 如果 getList 失败或返回空数据，尝试使用 pageQuery
    if (!projectData || projectData.length === 0) {
      try {
        console.log("🔄 尝试使用 pageQuery API...");
        const pageRes = await pageQuery({ pageNum: 1, pageSize: 1000 }); // 获取大量数据
        console.log("📊 pageQuery API 返回的原始数据:", pageRes);

        if (pageRes && (pageRes.success || pageRes.code === 200)) {
          projectData = pageRes.data?.records || pageRes.records || [];
        }
      } catch (pageError) {
        console.error("❌ pageQuery API 也调用失败:", pageError);
      }
    }

    console.log("📋 最终项目数据:", projectData);
    console.log("📋 数据长度:", projectData?.length);

    if (Array.isArray(projectData) && projectData.length > 0) {
      console.log("📋 第一个项目示例:", projectData[0]);

      // 重置统计数据
      Object.keys(categoryStats.value).forEach((level) => {
        Object.keys(categoryStats.value[level]).forEach((category) => {
          categoryStats.value[level][category] = 0;
        });
      });

      // 根据数据统计项目级别和类别的数量
      projectData.forEach((item, index) => {
        const level = item.projectLevel?.trim(); // 去除可能的空格
        const category = item.category?.trim(); // 去除可能的空格
        console.log(`项目 ${index + 1}: 级别="${level}", 类别="${category}"`);

        // 检查级别是否存在
        if (!categoryStats.value[level]) {
          console.log(`❌ 未知的项目级别: "${level}"`);
          console.log("📋 支持的级别:", Object.keys(categoryStats.value));
          return;
        }

        // 检查类别是否存在
        if (categoryStats.value[level][category] === undefined) {
          console.log(`❌ 未知的项目类别: "${category}"`);
          console.log(
            "📋 支持的类别:",
            Object.keys(categoryStats.value[level])
          );
          return;
        }

        categoryStats.value[level][category]++; // 确保每个类别下的项目都能正确统计
        console.log(
          `✅ 统计成功: ${level} - ${category} = ${categoryStats.value[level][category]}`
        );
      });

      console.log("📊 最终统计结果:", categoryStats.value);
    } else {
      console.warn("⚠️ 项目数据为空或不是数组");
    }

    // 延迟初始化图表，确保DOM已渲染
    setTimeout(() => {
      initChart(); // 初始化ECharts图表
    }, 100);
  } catch (error) {
    console.error("❌ 获取项目数据时出错:", error);
    // 出错时也初始化图表，显示空图表
    setTimeout(() => {
      initChart();
    }, 100);
  }
});

const categories = [
  "民间文学",
  "传统音乐",
  "传统舞蹈",
  "传统戏剧",
  "曲艺",
  "传统美术",
  "传统技艺",
  "传统医药",
  "民俗",
  "体育、游艺与杂技",
];

const initChart = () => {
  console.log("🎨 开始初始化图表...");

  if (!chartContainer.value) {
    console.error("❌ 找不到图表容器 chartContainer.value");
    return;
  }

  console.log("📐 图表容器:", chartContainer.value);
  console.log("📊 当前统计数据:", categoryStats.value);

  // 计算图表需要的最小宽度
  const categoryCount = categories.length;
  const minWidthPerCategory = 80; // 每个类别至少需要80px
  const minChartWidth = Math.max(categoryCount * minWidthPerCategory, 800);

  // 设置图表容器的最小宽度
  chartContainer.value.style.minWidth = minChartWidth + "px";

  const myChart = echarts.init(chartContainer.value);

  const containerWidth = chartContainer.value.offsetWidth;
  const maxCategoryWidth = Math.min((containerWidth / categoryCount) * 0.8, 60);

  console.log("📐 容器宽度:", containerWidth);
  console.log("📐 最小图表宽度:", minChartWidth);
  console.log("📐 每个类别最大宽度:", maxCategoryWidth);

  const option = {
    title: {
      text: "各级别非遗项目统计",
      subtext: "按类别分类",
      left: "center",
      textStyle: {
        fontSize: 20,
      },
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      top: "bottom",
      data: ["国家级", "市级", "自治区级"],
      textStyle: {
        fontSize: 14,
      },
    },
    grid: {
      left: "8%",
      right: "8%",
      bottom: "25%", // 给X轴标签留更多空间
      top: "20%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: categories,
      axisLabel: {
        rotate: 45,
        interval: 0,
        fontSize: 11,
        margin: 15,
        // 显示完整标签文字
        formatter: function (value) {
          return value;
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: "value",
      name: "数量",
      axisLabel: {
        fontSize: 14,
      },
    },
    series: [
      {
        name: "国家级",
        type: "bar",
        data: categories.map(
          (category) => categoryStats.value["国家级"][category]
        ),
        label: {
          show: true,
          position: "top",
          fontSize: 12,
        },
        barMaxWidth: maxCategoryWidth, // 设置每个类别的最大宽度
      },
      {
        name: "市级",
        type: "bar",
        data: categories.map(
          (category) => categoryStats.value["市级"][category]
        ),
        label: {
          show: true,
          position: "top",
          fontSize: 12,
        },
        barMaxWidth: maxCategoryWidth,
      },
      {
        name: "自治区级",
        type: "bar",
        data: categories.map(
          (category) => categoryStats.value["自治区级"][category]
        ),
        label: {
          show: true,
          position: "top",
          fontSize: 12,
        },
        barMaxWidth: maxCategoryWidth,
      },
    ],
  };

  console.log("🎯 图表配置项:", option);
  console.log("📊 国家级数据:", option.series[0].data);
  console.log("📊 市级数据:", option.series[1].data);
  console.log("📊 自治区级数据:", option.series[2].data);

  myChart.setOption(option); // 设置图表配置项

  // 添加窗口大小改变时的响应式处理
  const handleResize = () => {
    myChart.resize();
  };

  window.addEventListener("resize", handleResize);

  // 在组件卸载时移除事件监听器
  setTimeout(() => {
    myChart.resize(); // 初始化后立即调整一次大小
  }, 200);

  console.log("✅ 图表初始化完成");
};
</script>

<style scoped>
.admin-home {
  padding: 20px 20px; /* 添加左右内边距，让内容与边缘保持距离 */
  max-width: 1400px; /* 限制最大宽度，避免在大屏幕上过度拉伸 */
}

.carousel {
  width: 100%;
  height: 300px; /* 高度设置为合适的值 */
}

.banner-img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 确保图片保持比例并充满容器 */
  border-radius: 8px;
}

.card-group {
  margin-top: 30px;
}

.card-title {
  font-size: 16px;
  color: #666;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  margin-top: 10px;
}

.notice {
  margin: 20px 0;
}

.section {
  margin-top: 40px;
  width: 100%;
  overflow: hidden; /* 防止内容溢出 */
}

.chart-wrapper {
  width: 100%;
  overflow-x: auto; /* 允许水平滚动 */
  overflow-y: hidden;
  padding: 10px 0;
}

.chart-container {
  width: 100%;
  min-width: 800px; /* 设置最小宽度 */
  height: 500px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
  box-sizing: border-box;
}
</style>
