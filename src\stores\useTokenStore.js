import { defineStore } from "pinia";
import { ref } from "vue";

// 手动从 localStorage 初始化
const initToken = localStorage.getItem("token") || "";

export const useTokenStore = defineStore(
  "token",
  () => {
    const token = ref(initToken);

    const setToken = (newToken) => {
      token.value = newToken;
      localStorage.setItem("token", newToken); // 显式写入 localStorage
    };

    const removeToken = () => {
      token.value = "";
      localStorage.removeItem("token");
    };

    return {
      token,
      setToken,
      removeToken,
    };
  },
  {
    persist: true, // 可选：如果你用了插件也可以保留
  }
);
