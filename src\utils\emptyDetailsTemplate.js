export const emptyDetailsTemplate = {
    project_name: '',
    inclusion_in_lists: {
      national: {
        name: '',
        category: '',
        code: '',
        declaring_region_or_unit: '',
        inclusion_time: ''
      },
      provincial: {
        name: '',
        category: '',
        code: '',
        declaring_region_or_unit: '',
        inclusion_time: ''
      },
      municipal: {
        name: '',
        category: '',
        code: '',
        declaring_region_or_unit: '',
        inclusion_time: ''
      },
      county: {
        name: '',
        category: '',
        code: '',
        declaring_region_or_unit: '',
        inclusion_time: ''
      }
    },
    involved_ethnic_groups: '',
    geographical_location_and_distribution_area: '',
    project_introduction: '',
    inheritance_context: '',
    social_function_and_cultural_significance: '',
    protection_unit_info: {
      unit_name: '',
      legal_person_type: ''
    },
    protection_measures_and_effectiveness: '',
    funding_for_protection: {
      total_amount: 0,
      change_in_funding: 'maintained',
      funding_sources: ['government_input']
    },
    practice_situation: {
      has_fixed_practice_venue: false,
      practice_frequency_change: 'maintained',
      basic_practice_method_maintained: false,
      has_new_creation_or_development: false,
      audience_size_change: 'maintained',
      related_income_change: 'maintained'
    },
    inheritance_status: {
      representative_inheritors_at_all_levels: {
        national: 0,
        provincial: 0,
        municipal: 0,
        county: 0
      },
      change_in_number_of_apprentices_of_representative_inheritors: 'maintained',
      change_in_size_of_inheritance_group: 'maintained',
      age_structure_of_inheritance_group: 'reasonable_mix',
      has_venue_for_inheritance_activities: false,
      inheritance_group_capacity_building: {
        has_training_activities: false,
        training_activity_types: []
      }
    },
    promotion_and_display_status: {
      has_promotion_and_display_activities: false,
      activity_scales: [],
      frequency_change_of_activities: 'maintained',
      media_attention: 'low',
      into_school_situation: {
        included_in_curriculum: false,
        included_in_textbooks: false,
        has_campus_activities: false
      }
    },
    status_of_related_artifacts_and_sites: false,
    main_threats_to_inheritance_and_development: '',
    overall_survival_status: 'good',
    future_protection_plan: ''
  };
  