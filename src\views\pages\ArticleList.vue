<template>
  <div class="article-list" style="margin-top: 0px">
    <div class="search-bar">
      <el-input
        v-model="keyword"
        placeholder="搜索标题、作者或内容"
        clearable
        size="large"
        style="width: 400px"
        @keyup.enter="handleSearch"
      >
        <template #append>
          <el-button
            type="primary"
            @click="handleSearch"
            style="color: blue; width: 100px"
            >搜索</el-button
          >
          <el-button
            type="danger"
            @click="clearSearch"
            style="color: black; width: 100px"
            >清除</el-button
          >
        </template>
      </el-input>
    </div>

    <div v-if="loading" class="loading-text">正在加载，请稍候...</div>

    <div v-else-if="articles.length === 0" class="empty-text">
      暂无匹配的文章
    </div>

    <div v-else class="article-container">
      <div v-for="item in articles" :key="item.id" class="article-item">
        <h2 class="article-title" @click="goDetail(item.id)">
          {{ item.title }}
        </h2>
        <p style="color: #888">
          {{ item.author }} ·
          {{ dayjs(item.updatedTime).format("YYYY年MM月DD日 HH:mm:ss") }}
        </p>
      </div>
    </div>

    <div class="pagination">
      <button
        @click="changePage(currentPage - 1)"
        :disabled="currentPage === 1"
      >
        上一页
      </button>
      <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
      <button
        @click="changePage(currentPage + 1)"
        :disabled="currentPage === totalPages"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from "vue";
import { fetchArticlePage } from "@/api/article";
import dayjs from "dayjs";
import { useRouter, useRoute } from "vue-router"; // 加上 useRoute
const route = useRoute(); // 获取当前路由
const keyword = ref(route.query.keyword || "");
const articles = ref([]);
const currentPage = ref(Number(route.query.page) || 1);
const pageSize = 8;
const total = ref(0);

const router = useRouter();

const totalPages = computed(() => Math.ceil(total.value / pageSize));
const loading = ref(false);

const loadArticles = async () => {
  loading.value = true;
  try {
    const res = await fetchArticlePage({
      pageNum: currentPage.value,
      pageSize,
      keyword: keyword.value,
      status: "PUBLISHED", // 只请求已发布的文章
    });
    articles.value = res.data.records;
    total.value = res.data.total;
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  loadArticles();
};

const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const goDetail = (id) => {
  router.push({
    path: `/article/${id}`,
    query: {
      keyword: keyword.value,
      page: currentPage.value,
    },
  });
};

const formatDate = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleDateString();
};
const clearSearch = () => {
  keyword.value = "";
  handleSearch();
};

watch(currentPage, loadArticles);
onMounted(loadArticles);
</script>

<style scoped>
.article-list {
  max-width: 800px;
  margin: 60px auto;
  padding: 20px;
  background: #fefefe;
  border-radius: 8px;
}

.search-bar {
  text-align: center;
  margin-bottom: 20px;
}

.article-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.article-item {
  padding-bottom: 12px;
  border-bottom: 1px solid #ddd;
}

.article-title {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  cursor: pointer;
  transition: color 0.3s;
}

.article-title:hover {
  color: #409eff;
}

.article-meta {
  margin-top: 6px;
  font-size: 14px;
  color: #888;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  gap: 10px;
  font-size: 14px;
}

.pagination button {
  padding: 6px 14px;
  border: none;
  background-color: #409eff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.pagination button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.empty-text {
  text-align: center;
  margin: 50px 0;
  color: #999;
}
.search-bar {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}
.loading-text {
  text-align: center;
  margin: 50px 0;
  color: #666;
  font-size: 16px;
}
</style>
