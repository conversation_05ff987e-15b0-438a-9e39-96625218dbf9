// src/api/CountData.ts
//--------------------------------------------------
//  通用类型 & 接口定义
//--------------------------------------------------
import request from '@/request'

/** 后端统一响应结构 ── 与后端保持 1:1 对应 */
interface ApiResp<T> {
  code: number;
  data: T;
  message: string;
  success?: boolean; // 可选字段
}


/** 非遗项目条目（根据后端字段自行增删） */
export interface ProjectItem {
  id: number
  region: string
  projectLevel: string   // 国家级 / 市级 / 自治区级 …
  category: string       // 传统音乐 / 民俗 …
  name: string
  inheritor?: string
  src?: string
  content?: string
  createdTime?: string
  updatedTime?: string
}

/** 分类数量接口返回示例 */
export interface LevelStat {
  level: string          // “国家级”…
  count: number
}

//--------------------------------------------------
//  API 函数
//--------------------------------------------------

/**
 * 获取全部项目列表
 * 说明：
 *   1. 直接返回 ApiResp<ProjectItem[]>
 *   2. 由调用者自行判断 success / code 再取 resp.data
 */
export function getList (): Promise<ApiResp<ProjectItem[]>> {
  // 注意：request 拦截器已经返回了 response.data，所以这里直接返回
  return request.get('/heritageProjectEntity/list') as Promise<ApiResp<ProjectItem[]>>;
}

/**
 * 各等级项目数量统计
 * 同样返回完整响应体，让组件自己解包
 */
export function countByProjectLevel (): Promise<ApiResp<LevelStat[]>> {
  return request.get('/heritageProjectEntity/projectlevel') as Promise<ApiResp<LevelStat[]>>;
}

/* 如果还有其它统计接口，可按需追加 … */
