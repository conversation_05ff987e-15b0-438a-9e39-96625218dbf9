<template>
  <div class="article-detail" v-if="article">
    <div class="header">
      \
      <h1 class="title">{{ article.title }}</h1>
      <p class="meta">
        {{ article.author }} ·
        {{ dayjs(article.updatedTime).format("YYYY年MM月DD日 HH:mm:ss") }}
      </p>
      <el-divider />
    </div>

    <div class="content" v-html="article.content"></div>
    <!-- 只改按钮这一行，其余保持 -->
  </div>
  <button @click="goBack" class="back-button">⬅ 返回列表</button>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { fetchArticleDetail } from "@/api/article";
import dayjs from "dayjs";

const route = useRoute();
const router = useRouter();
const article = ref(null);

onMounted(async () => {
  const id = route.params.id;
  const res = await fetchArticleDetail(id);
  if (res.success) {
    article.value = res.data;
  }
});

const goBack = () => {
  router.push({
    path: "/article",
    query: {
      keyword: route.query.keyword || "",
      page: route.query.page || 1,
    },
  });
};
</script>

<style scoped>
.article-detail {
  max-width: 800px;
  margin: 0 auto;
  /* margin-top: -350px; */
  background-color: #a89171;
  padding: 30px 20px;
  border-radius: 8px;
  color: #333;
}
.header {
  text-align: center;
}
.title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
}
.meta {
  color: #f1f1f1;
  font-size: 14px;
  margin-bottom: 20px;
}
.content {
  background-color: #fff;
  padding: 20px;
  border-radius: 6px;
  line-height: 1.8;
  font-size: 16px;
}
/* 美化 + 顶部定位 */
.back-button {
  position: fixed; /* 相对于浏览器窗口固定 */
  right: 300px; /* 视口右边距 */
  bottom: 40px; /* 视口下边距 */
  background: #20b2aa;
  color: #fff;
  padding: 14px 36px; /* 比原先再大一点手感更好 */
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: 30px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: background 0.25s, transform 0.15s;
}

.back-button:hover {
  background: #1a998e;
  transform: translateY(-3px);
}
</style>
